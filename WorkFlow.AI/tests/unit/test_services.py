import pytest
from unittest.mock import AsyncMock, patch
from app.services.fe_gen.fe_gen_service import FrontendGenService
from app.models.pydantic_models.fe_gen import BoilerplateRequest

@pytest.mark.asyncio
async def test_generate_boilerplate():
    # Mock the provider factory and its methods
    with patch("app.services.fe_gen.fe_gen_service.ProviderFactory.create_provider") as mock_provider_factory:
        mock_provider = AsyncMock()
        mock_provider.generate_code.return_value = "# File: src/App.tsx\n```tsx\nconsole.log('Hello, World!');\n```"
        mock_provider_factory.return_value = mock_provider

        # Initialize the service
        service = FrontendGenService()

        # Create a mock request
        request = BoilerplateRequest(
            framework="react",
            language="typescript",
            styling="tailwindcss",
            project_name="test-app",
            state_management="redux",
            Bundler="vite",
            routing="react-router-dom",
            testing="jest",
            linting="eslint"
        )

        # Call the generate_boilerplate method
        response = await service.generate_boilerplate("project-id", "branch-id", request)

        # Assertions
        assert response["success"] is True
        assert "src/App.tsx" in response["file_structure"]["virtual_files"]
        assert "console.log('Hello, World!');" in response["file_structure"]["virtual_files"]["src/App.tsx"]["content"]