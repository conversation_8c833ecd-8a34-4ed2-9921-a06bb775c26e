import sys
import os
import shutil
import json

# Add the root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_get_screens():
    # Create the required directory structure
    base_dir = "projects/1/branches/main/screens"
    os.makedirs(base_dir, exist_ok=True)

    try:
        async with Async<PERSON>lient(app=app, base_url="http://testserver") as client:
            response = await client.get("/screens", params={"project_id": "1", "branch_id": "main"})
        
        assert response.status_code == 200
        assert isinstance(response.json(), str)  # Ensure the response is a string
        assert "screens" in response.json().lower()  # Check if "screens" is part of the response
    finally:
        # Clean up the created directory structure
        shutil.rmtree("projects", ignore_errors=True)

@pytest.mark.asyncio
async def test_post_screen():
    # Create the required directory structure
    base_dir = "projects/1/branches/main/screens"
    os.makedirs(base_dir, exist_ok=True)

    request_payload = {
        "framework": "react",
        "language": "typescript",
        "styling": "tailwindcss",
        "screen_name": "Home",
        "state_management": "redux",
        "Bundler": "vite",
        "routing": "react-router-dom",
        "testing": "vitest",
        "linting": "eslint",
        "additional_options": ["option1", "option2"],
        "description": "A test screen",
        "dependencies": ["axios"],
        "is_reusable": True,
        "has_tests": True,
    }

    try:
        async with AsyncClient(app=app, base_url="http://testserver") as client:
            response = await client.post(
                "/projects/1/branches/main/screen",
                content=json.dumps(request_payload),
                headers={"Content-Type": "application/json"}
            )

        assert response.status_code == 201
        response_data = response.json()
        assert response_data.get("success") is True
        assert "generated_code" in response_data
        assert os.path.exists(base_dir)  # Ensure the folder structure exists
    finally:
        # Clean up the created directory structure
        shutil.rmtree("projects", ignore_errors=True)