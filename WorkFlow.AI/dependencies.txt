aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
alembic==1.15.2
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
babel==2.17.0
backrefs==5.8
bcrypt==4.3.0
black==25.1.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
cryptography==44.0.2
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
ecdsa==0.19.1
fastapi==0.115.12
flake8==7.2.0
frozenlist==1.5.0
ghp-import==2.1.0
gitdb==4.0.12
GitPython==3.1.44
groq==0.22.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
iniconfig==2.1.0
isort==6.0.1
Jinja2==3.1.6
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.22
langchain-community==0.3.20
langchain-core==0.3.50
langchain-groq==0.3.2
langchain-ollama==0.3.0
langchain-openai==0.3.12
langchain-text-splitters==0.3.7
langsmith==0.3.24
Mako==1.3.9
Markdown==3.7
MarkupSafe==3.0.2
marshmallow==3.26.1
mccabe==0.7.0
mergedeep==1.3.4
mkdocs==1.6.1
mkdocs-get-deps==0.2.0
mkdocs-material==9.6.11
mkdocs-material-extensions==1.3.1
multidict==6.3.2
mypy==1.15.0
mypy-extensions==1.0.0
numpy==2.2.4
ollama==0.4.7
openai==1.70.0
orjson==3.10.16
packaging==24.2
paginate==0.5.7
passlib==1.7.4
pathspec==0.12.1
platformdirs==4.3.7
pluggy==1.5.0
propcache==0.3.1
psutil==7.0.0
pyasn1==0.4.8
pycodestyle==2.13.0
pycparser==2.22
pydantic==2.11.2
pydantic-settings==2.8.1
pydantic_core==2.33.1
pyflakes==3.3.2
PyGithub==2.6.1
Pygments==2.19.1
PyJWT==2.10.1
pymdown-extensions==10.14.3
PyNaCl==1.5.0
pytest==8.3.5
pytest-asyncio==0.26.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-github==0.1.0.dev0
python-gitlab==5.6.0
python-jose==3.4.0
PyYAML==6.0.2
pyyaml_env_tag==0.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rsa==4.9
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
SQLAlchemy==2.0.40
starlette==0.46.1
tenacity==9.1.2
tiktoken==0.9.0
tqdm==4.67.1
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.1
urllib3==2.3.0
uv==0.6.12
uvicorn==0.34.0
watchdog==6.0.0
wrapt==1.17.2
yarl==1.18.3
zstandard==0.23.0
