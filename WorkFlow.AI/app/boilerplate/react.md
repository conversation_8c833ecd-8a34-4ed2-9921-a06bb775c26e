{{project_name}}/
├── public/
│   ├── favicon.ico
│   └── index.html
│
├── src/
│   ├── assets/
│   │   ├── fonts/
│   │   ├── images/
│   │   └── styles/
│   │
│   ├── components/                     // Central index.ts exports all components
│   │   ├── ComponentName/
│   │   │   ├── ComponentName.tsx
│   │   │   ├── ComponentName.scss
│   │   └── index.ts                    // Exports all components from their folders
│   │
│   ├── constants/                      // Regex, Theme, Endpoints
│   ├── context/                        // Global contexts
│   ├── hoc/                            // Higher-order components like withAuth
│   ├── hooks/                          // Custom React hooks
│   ├── locales/                        // i18n translation files
│   ├── models/                         // TypeScript types & interfaces
│   ├── routes/                         // AppRouter and PrivateRoute
│
│   ├── screens/                        // Central index.ts exports all screens
│   │   ├── ScreenName/
│   │   │   ├── components/             // Screen-specific components (optional)
│   │   │   ├── ScreenName.tsx
│   │   │   ├── ScreenName.scss
│   │   └── index.ts                    // Exports all screens from their folders
│
│   ├── services/                       // API clients, service classes
│   ├── theme/                          // Tailwind theme extensions (colors, breakpoints)
│   ├── utils/                          // Utility functions
│   ├── App.tsx
│   ├── main.tsx
│   └── vite-env.d.ts
│
├── .gitignore
├── .eslintrc.json
├── .husky/
├── .env
├── .env.development
├── .prettierrc
├── package.json
├── README.md
├── tsconfig.json
├── .npmrc
├── index.scss
├── tailwind.config.js
├── postcss.config.js
└── vite.config.ts