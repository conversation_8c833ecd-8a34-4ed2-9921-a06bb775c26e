from fastapi import APIRouter, status, Depends
from pydantic import BaseModel
from typing import Dict, Any
from app.services.codegen.ollama_provider import OllamaProvider
from app.utils.helpers import extract_file_sections, save_generated_files

router = APIRouter()

# Request body model
class HealthCheckRequest(BaseModel):
    database_type: str = "mongodb"  # or "postgresql"
    project_id: str
    branch_id: str

# Response body model
class HealthCheckResponse(BaseModel):
    generated_code: str
    save_result: Dict[str, Any]

# Base prompt
def build_prompt(database_type: str) -> str:
    return f"""
    
You are a senior backend engineer. Write a clean, production-ready Express.js `/health` API endpoint that follows industry best practices:

- **Endpoint Structure**:
  - Create a comprehensive `/health` endpoint
  - Implement optional query parameters to control response detail level (e.g., ?verbose=true)
  - Use appropriate authentication/authorization if needed for detailed information

- **Metrics Collection**:
  - Measures **disk space** (free, total, usage %) in GB using the `diskusage` package
  - Measures **CPU load** (1, 5, 15-minute averages) using `os.loadavg()`
  - Measures **memory usage** (used, free, total) in GB using `os.totalmem()` and `os.freemem()`
  - Measures **system uptime** in a human-readable format
  - Tracks **response time** of the health check itself

- **Database Health**:
  - Checks **{{database_type}}** connection using {{"`mongoose.connection.readyState`" if database_type == "mongodb" else "`sequelize.authenticate()`"}}
  - Measures **connection pool** statistics (active, idle, waiting)
  - Implements **query timeout** with configurable threshold (default 5s)
  - Performs a simple **read query** to verify database responsiveness

- **External Dependencies**:
  - Checks connectivity to **external services** (configurable list)
  - Implements **circuit breaker pattern** for dependency checks
  - Uses **timeouts** for all external calls (default 3s)

- **Health Status Logic**:
  - Implements a **multi-level health status**: "healthy", "degraded", "unhealthy"
  - If disk usage > 85% or CPU load > 1.5 → "degraded"
  - If memory usage > 90% → "degraded"
  - If database is disconnected or query fails → "unhealthy"
  - If any critical external dependency is unreachable → "unhealthy"
  - Otherwise → "healthy"

- **Response Format**:
  - Responds using `res.json({{...}})` with appropriate HTTP status codes
  - Returns 200 for "healthy", 429 for "degraded", 503 for "unhealthy"
  - Includes timestamp, version, and environment information
  - Follows **JSON:API** or similar standardized format

- **Error Handling**:
  - Implements robust error handling for multiple error types:
    - 500 (Internal Server Error): For unexpected server errors
    - 503 (Service Unavailable): When the service is temporarily unavailable
    - 504 (Gateway Timeout): For database connection timeouts
    - 404 (Not Found): For missing resources
    - Custom error codes for specific database connection issues
  - Provides meaningful error messages and appropriate status codes
  - Logs errors with sufficient detail for debugging
  - Implements graceful degradation (partial results if some checks fail)

- **Performance & Security**:
  - Implements **caching** with configurable TTL (default 30s)
  - Uses **async/await** for all I/O operations
  - Implements **rate limiting** to prevent DoS attacks
  - Sets appropriate **security headers**

- **Observability**:
  - Integrates with **Prometheus** metrics (optional)
  - Adds structured logging with correlation IDs
  - Includes request tracing information
  - Provides custom metrics for monitoring systems

- **Configuration**:
  - Makes thresholds configurable via environment variables
  - Allows enabling/disabling specific checks
  - Supports different verbosity levels

Also create a package.json file for the generated project.

Wrap everything in proper error handling with try-catch blocks and implement comprehensive logging.
Output only pure code inside ``` (no explanations).
"""

@router.post("/generate-health-check", response_model=HealthCheckResponse, status_code=status.HTTP_200_OK)
async def generate_health_check(request: HealthCheckRequest) -> Dict[str, Any]:
    """
    POST API that generates Express.js health check API code using Ollama/CodeLlama
    and saves the generated code to the project/branch directory structure.
    """
    ollama = OllamaProvider()
    prompt = build_prompt(request.database_type)
    generated_code = await ollama.generate_code(prompt=prompt, language="javascript", temperature=0.2)
    # Clean up code block if needed
    if "```" in generated_code:
        code_parts = generated_code.split("```")
        for part in code_parts:
            if part.strip().startswith("javascript") or part.strip().startswith("js"):
                generated_code = part.replace("javascript", "").replace("js", "").strip()
                break
        else:
            for part in code_parts:
                if part.strip() and not part.strip() in ["javascript", "js"]:
                    generated_code = part.strip()
                    break
    # Extract file sections
    file_sections = extract_file_sections(generated_code)
    # If no file sections found, save to a default file
    if not file_sections:
        file_sections = {"healthcheck.js": generated_code}
    # Save generated files
    save_result = await save_generated_files(request.project_id, request.branch_id, file_sections)
    return {"generated_code": generated_code, "save_result": save_result}
