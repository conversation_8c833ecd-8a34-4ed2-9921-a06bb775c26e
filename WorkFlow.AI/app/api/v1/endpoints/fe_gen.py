import os
from fastapi import APIRouter, Depends, HTTPException, status, Body
from app.services.fe_gen.fe_gen_service import FrontendGenService
from app.models.pydantic_models.fe_gen import BoilerplateRequest, ScreenRequest, CommonComponentRequest


router = APIRouter(prefix="/fe", tags=["Frontend Generation"])


def get_fe_gen_service():
    return FrontendGenService()

    
@router.post("/boilerplate", status_code=status.HTTP_201_CREATED)
async def generate_boilerplate(
    project_id: str,
    branch_id: str,
    request: BoilerplateRequest = Body(...),
    service: FrontendGenService = Depends(get_fe_gen_service),
):
    """
    Generate complete frontend project boilerplate
    """
    response = await service.generate_boilerplate(project_id, branch_id, request)
    
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate boilerplate"),
        )
    
    return response

@router.get("/boilerplate/structure", status_code=status.HTTP_200_OK)
async def get_boilerplate_structure(
    project_id: str,
    branch_id: str,
    request: ScreenRequest = Body(...),
    service: FrontendGenService = Depends(get_fe_gen_service),
):
    """
    Retrieve the folder structure of the project and branch as a string
    """
    try:
        return await service.get_boilerplate_structure(project_id, branch_id)
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
        )
    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )

@router.post("/screen", status_code=status.HTTP_201_CREATED)
async def generate_screen(
    project_id: str,
    branch_id: str,
    request: ScreenRequest = Body(...),
    service: FrontendGenService = Depends(get_fe_gen_service),
):
    """
    Generate a specific screen for an existing project
    """
    try:
        response = await service.generate_screen(project_id, branch_id, request)
        if not response.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=response.get("message", "Failed to generate screen"),
            )
        return response
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
        )
    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )
@router.post("/common-component", status_code=status.HTTP_201_CREATED)
async def generate_common_components(
    project_id: str,
    branch_id: str,
    request: CommonComponentRequest = Body(...),
    service: FrontendGenService = Depends(get_fe_gen_service),
):
    """
    Generate a reusable common component based on the provided prompt.
    """
    response = await service.generate_common_component(project_id, branch_id, request)
    
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate common component"),
        )
    
    return response

