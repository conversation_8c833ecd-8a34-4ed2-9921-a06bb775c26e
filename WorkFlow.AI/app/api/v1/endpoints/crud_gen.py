from fastapi import APIRouter, Depends, HTTPException, status, Body
from app.services.crud_gen import CrudGenService
from app.models.pydantic_models.crud_gen import (
    AddApiRequest,
    SearchApiRequest,
    ListApiRequest,
    DeleteApiRequest,
    FetchApiRequest,
)

router = APIRouter()


# Use dependency injection for the service
# This makes the code more testable and follows best practices
def get_crud_gen_service():
    return CrudGenService()


@router.post("/add", status_code=status.HTTP_201_CREATED)
async def add_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: AddApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates API code (CREATE focus) and establishes the necessary folder structure based on natural language prompts, with real-time file persistence.

    This endpoint will:
    1. Generate API code (CREATE focus) based on the user's prompt.
    2. Save the generated code to appropriate files.
    3. Return the generated code and file information.
    """
    # Call the service method
    response = await service.add_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate API code (add)"),
        )

    return response


@router.post("/update", status_code=status.HTTP_200_OK)  # Typically 200 for update
async def update_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: AddApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates API code (UPDATE focus) based on natural language prompts, with real-time file persistence.

    This endpoint will:
    1. Generate API code (UPDATE focus) based on the user's prompt.
    2. Save the generated code to appropriate files.
    3. Return the generated code and file information.
    """
    # Call the service method
    response = await service.update_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate API code (update)"),
        )

    return response


@router.post("/overwrite", status_code=status.HTTP_201_CREATED)  # Can be 200 or 201
async def overwrite_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: AddApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates a complete CRUD API application based on natural language prompts, overwriting existing files.

    This endpoint will:
    1. Generate a full CRUD API application based on the user's prompt.
    2. Save the generated code to appropriate files, potentially overwriting.
    3. Return the generated code and file information.
    """
    # Call the service method
    response = await service.overwrite_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate API code (overwrite)"),
        )

    return response


@router.post(
    "/delete",
    status_code=status.HTTP_201_CREATED,
    summary="Generate Delete API Code",
    description="Creates optimized delete API code based on a natural language prompt with proper validation, error handling, and Swagger documentation.",
)
async def delete_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: DeleteApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates delete API code based on natural language prompts, with Swagger documentation and optimized implementation.

    This endpoint will:
    1. Generate delete API code based on the user's prompt
    2. Save the generated code to appropriate files
    3. Return the generated code and file information

    The generated delete API code will include:
    - Proper validation of input parameters
    - Appropriate error handling for cases where the resource doesn't exist
    - Transaction management where appropriate
    - Proper HTTP status codes (204 for successful deletion)
    - Appropriate logging of deletion operations
    - Security checks for authorization
    - Swagger/OpenAPI documentation
    - Optimized database queries

    Parameters:
    - **project_id**: Project identifier where the code will be generated
    - **branch_id**: Branch identifier where the code will be saved
    - **request**: DeleteApiRequest with a natural language prompt describing the delete API

    Returns:
    - DeleteApiResponse with success status, generated code, and file information

    Raises:
    - HTTPException 400: If API generation fails
    """
    # Call the service method
    response = await service.delete_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate delete API code"),
        )

    return response


@router.post("/search", status_code=status.HTTP_201_CREATED)
async def search_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: SearchApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates search API code based on a natural language prompt.

    This endpoint will:
    1. Generate search API code based on the user's prompt
    2. Implement filters extracted from the prompt
    3. Add pagination, sorting, and access control features
    4. Save the generated code to appropriate files

    The response includes:
    - success: Whether the operation was successful
    - message: Status message
    - generated_code: The complete generated code
    - file_info: Information about saved files
    """
    # Call the service method
    response = await service.search_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate search API code"),
        )

    return response


@router.post("/list", status_code=status.HTTP_201_CREATED)
async def list_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: ListApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates list API code based on a natural language prompt.

    This endpoint will:
    1. Generate list API code based on the user's prompt
    2. Implement proper query parameters (pagination, sorting, etc.)
    3. Add validation and error handling
    4. Save the generated code to appropriate files

    The response includes:
    - success: Whether the operation was successful
    - message: Status message
    - generated_code: The complete generated code
    - file_info: Information about saved files
    """
    # Call the service method
    response = await service.list_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate list API code"),
        )

    return response


@router.post(
    "/fetch",
    status_code=status.HTTP_201_CREATED,
    summary="Generate Fetch API Code",
    description="Creates optimized fetch/get API code based on a natural language prompt with proper validation, error handling, and Swagger documentation.",
)
async def fetch_resource_endpoint(
    project_id: str,
    branch_id: str,
    request: FetchApiRequest = Body(...),
    service: CrudGenService = Depends(get_crud_gen_service),
):
    """
    Dynamically generates fetch/get API code based on natural language prompts, with Swagger documentation and optimized implementation.

    This endpoint will:
    1. Generate fetch API code based on the user's prompt
    2. Save the generated code to appropriate files
    3. Return the generated code and file information

    The generated fetch API code will include:
    - Proper validation that resources exist
    - Appropriate error handling (404 for not found)
    - Optimized database queries
    - Swagger/OpenAPI documentation
    - Proper logging of fetch operations
    - Security checks for authorization where needed

    Parameters:
    - **project_id**: Project identifier where the code will be generated
    - **branch_id**: Branch identifier where the code will be saved
    - **request**: FetchApiRequest with a natural language prompt describing the fetch API

    Returns:
    - FetchApiResponse with success status, generated code, and file information

    Raises:
    - HTTPException 400: If API generation fails
    """
    # Call the service method
    response = await service.fetch_api(project_id, branch_id, request)

    # Handle errors
    if not response.get("success", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.get("message", "Failed to generate fetch API code"),
        )

    return response
