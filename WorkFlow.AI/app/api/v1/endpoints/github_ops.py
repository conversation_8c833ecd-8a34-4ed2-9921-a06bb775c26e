from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, List, Optional
from app.models.pydantic_models.project import (
    ProjectCreateRequest,
    CodePushRequest,
    CodePullRequest,
)

from app.services.git_ops.github import GitHubService, GitHubException
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


def get_github_service():
    try:
        return GitHubService()
    except GitHubException as e:
        logger.error(f"Failed to initialize GitHub service: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"GitHub service initialization failed: {str(e)}",
        )


@router.get("/projects", status_code=status.HTTP_200_OK)
async def list_github_projects(
    search: Optional[str] = None,
    limit: int = 5,
    github_service: GitHubService = Depends(get_github_service),
) -> List[Dict]:
    """
    List GitHub repositories accessible to the current user.
    """
    try:
        logger.info(
            f"API request to list GitHub repositories, search: {search}, limit: {limit}"
        )
        result = github_service.list_projects(search=search, limit=limit)
        logger.info(f"Successfully listed {len(result)} GitHub repositories")
        return result
    except GitHubException as e:
        logger.error(f"Failed to list GitHub repositories: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing GitHub repositories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.get("/projects/{project_id}/branches", status_code=status.HTTP_200_OK)
async def get_project_branches(
    project_id: str,
    github_service: GitHubService = Depends(get_github_service),
) -> List[Dict]:
    """
    Get branches for a GitHub repository.
    """
    try:
        logger.info(
            f"API request to get branches for GitHub repository ID {project_id}"
        )
        result = github_service.get_project_branches(project_id=project_id)
        logger.info(
            f"Successfully retrieved branches for GitHub repository ID {project_id}"
        )
        return result
    except GitHubException as e:
        logger.error(f"Failed to get branches for GitHub repository: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting branches: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/project", status_code=status.HTTP_201_CREATED)
async def create_github_project(
    request: ProjectCreateRequest,
    github_service: GitHubService = Depends(get_github_service),
) -> Dict:
    """
    Create a new GitHub repository.
    """
    try:
        logger.info(f"API request to create GitHub repository: {request.name}")
        result = github_service.create_project(
            name=request.name,
            description=request.description,
            visibility=request.visibility,
            initialize_with_readme=request.initialize_with_readme,
        )
        logger.info(f"Successfully created GitHub repository: {request.name}")
        return result
    except GitHubException as e:
        logger.error(f"Failed to create GitHub repository: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating GitHub repository: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/push", status_code=status.HTTP_200_OK)
async def push_code_to_github(
    request: CodePushRequest,
    github_service: GitHubService = Depends(get_github_service),
) -> Dict:
    """
    Advanced push code to a GitHub repository with automatic branch handling.
    This endpoint first checks if the repository and branch exist, then pushes code accordingly.
    """
    try:
        logger.info(
            f"API request for advanced push to repository ID {request.project_id} on branch {request.branch}"
        )
        result = github_service.push_code(
            project_id=request.project_id,
            local_path=request.local_path,
            branch=request.branch,
            commit_message=request.commit_message,
            create_branch=request.create_branch,
        )
        logger.info(
            f"Successfully pushed code to GitHub repository ID {request.project_id} on branch {request.branch}"
        )
        return result
    except GitHubException as e:
        logger.error(f"Failed to perform advanced push to GitHub: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during advanced push to GitHub: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/pull", status_code=status.HTTP_200_OK)
async def pull_code_from_github(
    request: CodePullRequest,
    github_service: GitHubService = Depends(get_github_service),
) -> Dict:
    """
    Pull latest changes from a GitLab repository branch into a local directory.
    Clones the repo if the local path doesn't exist or isn't a git repo.
    """
    try:
        logger.info(
            f"API request to pull code for project ID {request.project_id}, branch '{request.branch}' into {request.local_path}"
        )
        result = github_service.pull_code(
            project_id=request.project_id,
            branch=request.branch,
            local_path=request.local_path,
        )
        logger.info(
            f"Successfully pulled/cloned code for project ID {request.project_id}, branch '{request.branch}'"
        )
        return result
    except GitHubException as e:
        logger.error(f"Failed to pull code from GitLab: {str(e)}")
        # Use 400 for client errors (e.g., bad project ID, branch not found)
        # Use 500 for internal server errors (e.g., git command failure, unexpected issues)
        # Since GitLabException can cover both, we might refine this, but 400 is reasonable default
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during code pull: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )