from fastapi import APIRouter, Depends, BackgroundTasks
from typing import Dict, Any, List
from app.services.health.health_service import HealthService

router = APIRouter()


@router.get("/")
async def health_check(health_service: HealthService = Depends(HealthService)):
    """
    Basic health check endpoint.
    Returns a simple status response indicating the API is running.
    """
    return {"status": "healthy"}


@router.get("/detailed")
async def detailed_health_check(health_service: HealthService = Depends(HealthService)):
    """
    Detailed health check that performs comprehensive system and services check.
    Includes CPU, memory, disk usage, and status of critical services.
    """
    return await health_service.get_complete_health_check()


@router.get("/system")
async def system_health(health_service: HealthService = Depends(HealthService)):
    """
    Get detailed system health metrics including CPU, memory, and disk usage.
    """
    return await health_service.get_system_health()


@router.get("/ollama")
async def ollama_status(health_service: HealthService = Depends(HealthService)):
    """
    Check Ollama server status and available models.
    """
    return await health_service.check_ollama_status()


@router.post("/ollama/start")
async def start_ollama(health_service: HealthService = Depends(HealthService)):
    """
    Start Ollama server if it's not running.
    """
    return await health_service.start_ollama_server()


@router.post("/ollama/models/{model_name}")
async def pull_model(
    model_name: str,
    background_tasks: BackgroundTasks,
    health_service: HealthService = Depends(HealthService),
):
    """
    Pull a specific Ollama model.
    This operation runs in the background to avoid blocking the API.
    """
    # For immediate pull
    # return await health_service.pull_model(model_name)

    # For background pull
    background_tasks.add_task(health_service.pull_model, model_name)
    return {
        "action": "started",
        "model": model_name,
        "message": f"Started pulling model {model_name} in the background",
    }
