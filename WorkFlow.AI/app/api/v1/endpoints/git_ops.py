from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, List, Optional
from typing import Union # Added Union
from app.models.pydantic_models.project import (
    ProjectCreateRequest,
    CodePushRequest,
    CodePullRequest, # Added CodePullRequest
)

from app.services.git_ops.gitlab import GitLabService, GitLabException
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


def get_gitlab_service():
    try:
        return GitLabService()
    except GitLabException as e:
        logger.error(f"Failed to initialize GitLab service: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"GitLab service initialization failed: {str(e)}",
        )


@router.get("/projects", status_code=status.HTTP_200_OK)
async def list_gitlab_projects(
    search: Optional[str] = None,
    limit: int = 5,
    gitlab_service: GitLabService = Depends(get_gitlab_service),
) -> List[Dict]:
    """
    List GitLab projects accessible to the current user.
    """
    try:
        logger.info(
            f"API request to list GitLab projects, search: {search}, limit: {limit}"
        )
        result = gitlab_service.list_projects(search=search, limit=limit)
        logger.info(f"Successfully listed {len(result)} GitLab projects")
        return result
    except GitLabException as e:
        logger.error(f"Failed to list GitLab projects: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing GitLab projects: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/pull", status_code=status.HTTP_200_OK)
async def pull_code_from_gitlab(
    request: CodePullRequest,
    gitlab_service: GitLabService = Depends(get_gitlab_service),
) -> Dict:
    """
    Pull latest changes from a GitLab repository branch into a local directory.
    Clones the repo if the local path doesn't exist or isn't a git repo.
    """
    try:
        logger.info(
            f"API request to pull code for project ID {request.project_id}, branch '{request.branch}' into {request.local_path}"
        )
        result = gitlab_service.pull_code(
            project_id=request.project_id,
            branch=request.branch,
            local_path=request.local_path,
        )
        logger.info(
            f"Successfully pulled/cloned code for project ID {request.project_id}, branch '{request.branch}'"
        )
        return result
    except GitLabException as e:
        logger.error(f"Failed to pull code from GitLab: {str(e)}")
        # Use 400 for client errors (e.g., bad project ID, branch not found)
        # Use 500 for internal server errors (e.g., git command failure, unexpected issues)
        # Since GitLabException can cover both, we might refine this, but 400 is reasonable default
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during code pull: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.get("/projects/{project_id}/branches", status_code=status.HTTP_200_OK)
async def get_project_branches(
    project_id: str,
    gitlab_service: GitLabService = Depends(get_gitlab_service),
) -> List[Dict]:
    """
    Get branches for a GitLab project.
    """
    try:
        logger.info(f"API request to get branches for GitLab project ID {project_id}")
        result = gitlab_service.get_project_branches(project_id=project_id)
        logger.info(
            f"Successfully retrieved branches for GitLab project ID {project_id}"
        )
        return result
    except GitLabException as e:
        logger.error(f"Failed to get branches for GitLab project: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting branches: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/project", status_code=status.HTTP_201_CREATED)
async def create_gitlab_project(
    request: ProjectCreateRequest,
    gitlab_service: GitLabService = Depends(get_gitlab_service),
) -> Dict:
    """
    Create a new GitLab repository.
    """
    try:
        logger.info(f"API request to create GitLab project: {request.name}")
        result = gitlab_service.create_project(
            name=request.name,
            description=request.description,
            visibility=request.visibility,
            initialize_with_readme=request.initialize_with_readme,
        )
        logger.info(f"Successfully created GitLab project: {request.name}")
        return result
    except GitLabException as e:
        logger.error(f"Failed to create GitLab project: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating GitLab project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post("/push", status_code=status.HTTP_200_OK)
async def push_code_to_gitlab(
    request: CodePushRequest,
    gitlab_service: GitLabService = Depends(get_gitlab_service),
) -> Dict:
    """
    Advanced push code to a GitLab repository with automatic branch handling.
    This endpoint first checks if the project and branch exist, then pushes code accordingly.
    """
    try:
        logger.info(
            f"API request for advanced push to project ID {request.project_id} on branch {request.branch}"
        )
        result = gitlab_service.push_code(
            project_id=request.project_id,
            local_path=request.local_path,
            branch=request.branch,
            commit_message=request.commit_message,
            create_branch=request.create_branch,
        )
        logger.info(
            f"Successfully pushed code to GitLab project ID {request.project_id} on branch {request.branch}"
        )
        return result
    except GitLabException as e:
        logger.error(f"Failed to perform advanced push to GitLab: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during advanced push to GitLab: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )
