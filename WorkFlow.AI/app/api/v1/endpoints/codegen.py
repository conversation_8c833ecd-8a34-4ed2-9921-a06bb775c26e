from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from app.models.pydantic_models.codegen import (
    CodeGenerationRequest, 
    CodeGenerationWithContextRequest,
    CodeGenerationResponse,
    ValidationRequest,
    ValidationResponse,
    SupportedProvidersResponse,
    ProviderInfo,
    FileOperationRequest,
    FileListRequest,
    FileListResponse,
    ContextFilesRequest,
    ContextFile
)
from app.services.codegen.code_generation_service import CodeGenerationService
from app.services.codegen.validation import ValidationService
from app.services.codegen.file_ops import FileOperationsService
import os

router = APIRouter()

# Dependency injection
def get_code_gen_service() -> CodeGenerationService:
    return CodeGenerationService()

def get_validation_service() -> ValidationService:
    return ValidationService()

def get_file_ops_service() -> FileOperationsService:
    workspace_dir = os.getenv("WORKSPACE_DIR", os.getcwd())
    return FileOperationsService(base_dir=workspace_dir)

@router.post("/generate", response_model=CodeGenerationResponse, status_code=status.HTTP_200_OK)
async def generate_code(
    request: CodeGenerationRequest,
    code_gen_service: CodeGenerationService = Depends(get_code_gen_service),
    validation_service: ValidationService = Depends(get_validation_service)
) -> CodeGenerationResponse:
    """
    Generate code based on the provided prompt.
    """
    try:
        # Generate code
        generated_code = await code_gen_service.generate_code(
            prompt=request.prompt,
            language=request.language,
            provider_name=request.provider,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            additional_params=request.additional_params
        )
        
        # Get the actual provider and model used
        provider = request.provider or code_gen_service.default_provider
        model = request.model or "default"  # This is a simplification, in reality you'd get the actual model
        
        # Validate code (optional)
        validated = False
        validation_message = None
        
        try:
            is_valid, message = await validation_service.validate_code(
                code=generated_code,
                language=request.language
            )
            validated = is_valid
            validation_message = message
        except Exception as e:
            validation_message = f"Validation error: {str(e)}"
        
        # Return response
        return CodeGenerationResponse(
            code=generated_code,
            language=request.language,
            provider=provider,
            model=model,
            validated=validated,
            validation_message=validation_message
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating code: {str(e)}"
        )

@router.post("/generate-with-context", response_model=CodeGenerationResponse, status_code=status.HTTP_200_OK)
async def generate_code_with_context(
    request: CodeGenerationWithContextRequest,
    code_gen_service: CodeGenerationService = Depends(get_code_gen_service),
    validation_service: ValidationService = Depends(get_validation_service)
) -> CodeGenerationResponse:
    """
    Generate code based on the provided prompt and context files.
    """
    try:
        # Generate code with context
        generated_code = await code_gen_service.generate_code_with_context(
            prompt=request.prompt,
            context_files=[{"filename": cf.filename, "content": cf.content} for cf in request.context_files],
            language=request.language,
            provider_name=request.provider,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            additional_params=request.additional_params
        )
        
        # Get the actual provider and model used
        provider = request.provider or code_gen_service.default_provider
        model = request.model or "default"  # This is a simplification, in reality you'd get the actual model
        
        # Validate code (optional)
        validated = False
        validation_message = None
        
        try:
            is_valid, message = await validation_service.validate_code(
                code=generated_code,
                language=request.language
            )
            validated = is_valid
            validation_message = message
        except Exception as e:
            validation_message = f"Validation error: {str(e)}"
        
        # Return response
        return CodeGenerationResponse(
            code=generated_code,
            language=request.language,
            provider=provider,
            model=model,
            validated=validated,
            validation_message=validation_message
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating code: {str(e)}"
        )

@router.post("/validate", response_model=ValidationResponse, status_code=status.HTTP_200_OK)
async def validate_code(
    request: ValidationRequest,
    validation_service: ValidationService = Depends(get_validation_service)
) -> ValidationResponse:
    """
    Validate the provided code.
    """
    try:
        is_valid, message = await validation_service.validate_code(
            code=request.code,
            language=request.language
        )
        
        return ValidationResponse(
            is_valid=is_valid,
            message=message
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating code: {str(e)}"
        )

@router.get("/providers", response_model=SupportedProvidersResponse, status_code=status.HTTP_200_OK)
async def get_providers(
    code_gen_service: CodeGenerationService = Depends(get_code_gen_service)
) -> SupportedProvidersResponse:
    """
    Get a list of available providers and their supported models.
    """
    try:
        available_providers = await code_gen_service.get_available_providers()
        provider_models = await code_gen_service.get_supported_models()
        
        provider_infos = [
            ProviderInfo(
                name=provider,
                models=provider_models.get(provider, [])
            )
            for provider in available_providers
        ]
        
        return SupportedProvidersResponse(
            providers=provider_infos
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting providers: {str(e)}"
        )

@router.post("/file/read", status_code=status.HTTP_200_OK)
async def read_file(
    request: FileOperationRequest,
    file_ops_service: FileOperationsService = Depends(get_file_ops_service)
) -> Dict[str, str]:
    """
    Read file content from the specified path.
    """
    try:
        content = await file_ops_service.read_file(request.file_path)
        return {"content": content}
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File not found: {request.file_path}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading file: {str(e)}"
        )

@router.post("/file/write", status_code=status.HTTP_200_OK)
async def write_file(
    request: FileOperationRequest,
    file_ops_service: FileOperationsService = Depends(get_file_ops_service)
) -> Dict[str, str]:
    """
    Write content to the specified file path.
    """
    try:
        if request.content is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content is required for write operation"
            )
        
        await file_ops_service.write_file(
            file_path=request.file_path,
            content=request.content,
            create_dirs=request.create_dirs
        )
        
        return {"message": f"File written: {request.file_path}"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error writing file: {str(e)}"
        )

@router.post("/file/list", response_model=FileListResponse, status_code=status.HTTP_200_OK)
async def list_files(
    request: FileListRequest,
    file_ops_service: FileOperationsService = Depends(get_file_ops_service)
) -> FileListResponse:
    """
    List files in the specified directory.
    """
    try:
        files = await file_ops_service.list_files(
            directory=request.directory,
            pattern=request.pattern
        )
        
        return FileListResponse(files=files)
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Directory not found: {request.directory}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing files: {str(e)}"
        )

@router.post("/file/context", status_code=status.HTTP_200_OK)
async def get_context_files(
    request: ContextFilesRequest,
    file_ops_service: FileOperationsService = Depends(get_file_ops_service)
) -> Dict[str, List[ContextFile]]:
    """
    Get a list of files with their content for context.
    """
    try:
        context_files = await file_ops_service.get_context_files(
            directory=request.directory,
            include_patterns=request.include_patterns,
            exclude_patterns=request.exclude_patterns,
            max_files=request.max_files,
            max_file_size_kb=request.max_file_size_kb
        )
        
        # Convert to Pydantic models
        result = [
            ContextFile(filename=cf["filename"], content=cf["content"])
            for cf in context_files
        ]
        
        return {"context_files": result}
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Directory not found: {request.directory}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting context files: {str(e)}"
        )
