from fastapi import APIRouter
from app.api.v1.endpoints import crud_gen, git_ops, github_ops, health, fe_gen

api_router = APIRouter()

# Include routers for different endpoints
api_router.include_router(
    crud_gen.router,
    prefix="/projects/{project_id}/branches/{branch_id}/code",
    tags=["CRUD Generations"],
)
api_router.include_router(
    git_ops.router, prefix="/git/gitlab", tags=["GitLab Operations"]
)
api_router.include_router(
    github_ops.router, prefix="/git/github", tags=["GitHub Operations"]
)
api_router.include_router(health.router, prefix="/health", tags=["Health"])
api_router.include_router(
    fe_gen.router,
    prefix="/projects/{project_id}/branches/{branch_id}"
)