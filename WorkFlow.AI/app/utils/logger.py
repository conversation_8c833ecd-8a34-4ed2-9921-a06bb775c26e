import logging
import os
from pathlib import Path

# Create logs directory if it doesn't exist
logs_dir = Path("./logs")
logs_dir.mkdir(exist_ok=True)


# Configure the root logger
def setup_logger(log_level=logging.DEBUG):
    """
    Configure the root logger with the specified log level.

    Args:
        log_level: The logging level (default: DEBUG)
    """
    # Create formatters for different handlers
    console_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s"
    )

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Remove any existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

    # Add file handler for all logs
    file_handler = logging.FileHandler(os.path.join(logs_dir, "application.log"))
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(log_level)
    root_logger.addHandler(file_handler)

    # Add file handler specifically for errors
    error_handler = logging.FileHandler(os.path.join(logs_dir, "errors.log"))
    error_handler.setFormatter(file_formatter)
    error_handler.setLevel(logging.ERROR)
    root_logger.addHandler(error_handler)


# Initialize the logger when the module is imported
setup_logger()


def get_logger(name):
    """
    Get a logger instance with the specified name.

    Args:
        name: The name of the logger (typically __name__ from the calling module)

    Returns:
        A configured logger instance
    """
    return logging.getLogger(name)
