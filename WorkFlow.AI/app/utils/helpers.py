from typing import Dict, <PERSON><PERSON>
import re
from pathlib import Path
from app.templates.template import <PERSON>NG<PERSON>AGES, DATABASES
from app.utils.logger import get_logger

logger = get_logger(__name__)

def extract_language_from_prompt(prompt: str) -> Tuple[str, str]:
    """
    Extract programming language from the prompt.

    Args:
        prompt: User's prompt

    Returns:
        Tuple containing (language, processed_prompt)
    """
    # Define common programming languages to look for
    languages = LANGUAGES

    # Convert prompt to lowercase for easier matching
    prompt_lower = prompt.lower()

    # Look for language keywords in the prompt
    detected_language = "python"  # Default to Python if no language is detected

    for language, keywords in languages.items():
        for keyword in keywords:
            if keyword in prompt_lower:
                detected_language = language
                break
        if detected_language != "python":  # Stop if we found a non-default language
            break

    # Return the detected language and original prompt
    return detected_language, prompt

def extract_database_from_prompt(prompt: str) -> Tuple[str, str]:
    """
    Extract database type from the prompt.

    Args:
        prompt: User's prompt

    Returns:
        <PERSON>ple containing (database_type, processed_prompt)
    """
    # Define common database types to look for
    databases = DATABASES

    # Convert prompt to lowercase for easier matching
    prompt_lower = prompt.lower()

    # Look for database keywords in the prompt
    detected_db = "mysql"  # Default to MySQL if no database is detected

    for db, keywords in databases.items():
        for keyword in keywords:
            if keyword in prompt_lower:
                detected_db = db
                break
        if detected_db != "mysql":  # Stop if we found a non-default database
            break

    # Return the detected database and original prompt
    return detected_db, prompt

def extract_file_sections(text: str) -> Dict[str, str]:
    """
    Extracts file paths and their content from the generated text.

    Args:
        text: Generated text containing file paths and code

    Returns:
        Dictionary mapping file paths to their content
    """
    file_sections = {}

    # Use regex to find file sections with the format:
    # # File: path/to/file.ext
    # ```language
    # code
    # ```
    file_pattern = r"#\s*File:\s*([^\n]+)[\s\n]+```[^\n]*\n(.*?)```"
    matches = re.findall(file_pattern, text, re.DOTALL)

    for file_path, code_content in matches:
        # Clean the file path
        file_path = file_path.strip()
        file_sections[file_path] = code_content.strip()

    # If no matches found with the regex, try a fallback method
    if not file_sections:
        current_file = None
        current_content = []
        in_code_block = False

        lines = text.split("\n")

        for line in lines:
            # Look for file path markers
            if re.match(r"#\s*File:\s*", line):
                # If we were processing a file, save it
                if current_file and current_content:
                    file_sections[current_file] = "\n".join(current_content)
                    current_content = []

                # Extract the file path
                current_file = re.sub(r"#\s*File:\s*", "", line).strip()

            # Check for code block markers
            elif line.strip().startswith("```"):
                in_code_block = not in_code_block
                # Skip the code block markers
                continue

            # If we're in a code block and have a current file, collect content
            elif in_code_block and current_file:
                current_content.append(line)

        # Save the last file if any
        if current_file and current_content:
            file_sections[current_file] = "\n".join(current_content)

    return file_sections

async def save_generated_files(
    project_id: str, branch_id: str, file_sections: Dict[str, str]
) -> Dict[str, any]:
    """
    Save the generated files to the file system.

    Args:
        project_id: Project identifier
        branch_id: Branch identifier
        file_sections: Dictionary mapping file paths to their content

    Returns:
        Dictionary with file paths and status information
    """
    saved_files = []
    failed_files = []

    # logger.info(f"[FileSaver] Saving files to base path: {file_path.resolve()}")

    try:
        # Create base directory for this project and branch
        base_dir = Path(f"./projects/{project_id}/branches/{branch_id}")

        for file_path, content in file_sections.items():
            try:
                # Clean up the path to ensure it's safe
                # Remove any path traversal attempts
                clean_path = file_path.replace("..", "").lstrip("/")

                # Skip if path looks suspicious or malformed
                if not clean_path or clean_path.startswith(("http:", "https:")):
                    failed_files.append(
                        {"path": file_path, "error": "Invalid or unsafe path"}
                    )
                    continue

                # Create full path
                full_path = base_dir / clean_path

                # Create parent directories if they don't exist
                full_path.parent.mkdir(parents=True, exist_ok=True)

                # Logging file path and size
                logger.debug(f"[FileSaver] Writing file: {full_path} (size: {len(content)} bytes)")

                # Write content to file
                with open(full_path, "w", encoding="utf-8") as f:
                    f.write(content)

                saved_files.append(str(full_path))

            except Exception as e:
                logger.error(f"[FileSaver] Failed to save {file_path}: {e}", exc_info=True)
                failed_files.append({"path": file_path, "error": str(e)})

        return {
            "saved_files": saved_files,
            "failed_files": failed_files,
            "status": "Files created" if saved_files else "No files created",
        }

    except Exception as e:
        return {"error": str(e), "status": "File creation failed"}
