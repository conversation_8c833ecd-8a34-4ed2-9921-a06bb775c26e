from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator

class ContextFile(BaseModel):
    """Model for a context file."""
    filename: str = Field(..., description="Filename including path")
    content: str = Field(..., description="File content")


class CodeGenerationRequest(BaseModel):
    """Model for a code generation request."""
    prompt: str = Field(..., description="Description of the code to generate")
    language: str = Field(..., description="Programming language")
    provider: Optional[str] = Field(None, description="LLM provider (openai, groq, ollama)")
    model: Optional[str] = Field(None, description="LLM model name")
    temperature: float = Field(0.7, description="Sampling temperature", ge=0.0, le=1.0)
    max_tokens: int = Field(2048, description="Maximum tokens to generate", ge=1)
    additional_params: Optional[Dict[str, Any]] = Field(None, description="Additional provider-specific parameters")


class CodeGenerationWithContextRequest(CodeGenerationRequest):
    """Model for a code generation request with context files."""
    context_files: List[ContextFile] = Field([], description="List of context files")


class CodeGenerationResponse(BaseModel):
    """Model for a code generation response."""
    code: str = Field(..., description="Generated code")
    language: str = Field(..., description="Programming language")
    provider: str = Field(..., description="LLM provider used")
    model: str = Field(..., description="LLM model used")
    validated: bool = Field(False, description="Whether the code was validated")
    validation_message: Optional[str] = Field(None, description="Validation message")


class ValidationRequest(BaseModel):
    """Model for a code validation request."""
    code: str = Field(..., description="Code to validate")
    language: str = Field(..., description="Programming language")
    filename: Optional[str] = Field(None, description="Optional filename")


class ValidationResponse(BaseModel):
    """Model for a code validation response."""
    is_valid: bool = Field(..., description="Whether the code is valid")
    message: str = Field(..., description="Validation message")


class ProviderInfo(BaseModel):
    """Model for a provider and its models."""
    name: str = Field(..., description="Provider name")
    models: List[str] = Field(..., description="Available models")


class SupportedProvidersResponse(BaseModel):
    """Model for the response listing supported providers and models."""
    providers: List[ProviderInfo] = Field(..., description="List of providers and their models")


class FileOperationRequest(BaseModel):
    """Model for a file operation request."""
    file_path: str = Field(..., description="Path to the file")
    content: Optional[str] = Field(None, description="Content to write (for write operations)")
    create_dirs: bool = Field(True, description="Whether to create parent directories (for write operations)")


class FileListRequest(BaseModel):
    """Model for a file listing request."""
    directory: str = Field(".", description="Directory to list files from")
    pattern: Optional[str] = Field(None, description="Glob pattern to filter files")


class FileListResponse(BaseModel):
    """Model for a file listing response."""
    files: List[str] = Field(..., description="List of file paths")


class ContextFilesRequest(BaseModel):
    """Model for a context files request."""
    directory: str = Field(".", description="Directory to get files from")
    include_patterns: Optional[List[str]] = Field(None, description="List of glob patterns to include")
    exclude_patterns: Optional[List[str]] = Field(None, description="List of glob patterns to exclude")
    max_files: int = Field(10, description="Maximum number of files")
    max_file_size_kb: int = Field(100, description="Maximum file size in KB")
