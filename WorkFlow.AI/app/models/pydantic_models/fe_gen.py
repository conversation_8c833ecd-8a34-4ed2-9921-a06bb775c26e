# models/pydantic_models/fe_gen.py
from pydantic import BaseModel
from typing import List, Optional

class BoilerplateRequest(BaseModel):
    framework: str = "react"  # Default to React
    language: str = "typescript"  # Default to TypeScript
    styling: str = "tailwindcss"  # Default to Tailwind CSS
    project_name: str = "my-app"  # Default project name
    state_management: str = "mobx"  # Default to MobX
    Bundler: str = "vite"  # Default to Vite
    routing: str = "react-router-dom"  # Default to React Router
    testing: str = "vitest"  # Default to Vitest
    linting: str = "eslint"  # Default to ESLint
    additional_options: List[str] = [""]  # Default options

class CommonComponentRequest(BaseModel):
    framework: str = "react"
    language: str = "typescript"
    styling: str = "Material UI (MUI v5)"
    prompt:str = "Create a reusable React component using Material UI(MUI v5) for a button. The component should accept three props: id, name, and label."
    additional_options: List[str] = [""] # Default options

class ScreenRequest(BaseModel):
    framework: str = "react"  # Default to React
    language: str = "typescript"  # Default to TypeScript
    styling: str = "tailwindcss"  # Default to Tailwind CSS
    screen_name: str = "Home"  # Default screen name
    state_management: str = "mobx"  # Default to MobX
    bundler: str = "vite"  # Default to Vite (fixed from PascalCase to snake_case)
    routing: str = "react-router-dom"  # Default to React Router
    testing: str = "vitest"  # Default to Vitest
    linting: str = "eslint"  # Default to ESLint
    css_preprocessor: Optional[str] = None  # Optional CSS preprocessor (scss, less, etc.)
    css_modules: bool = False  # Whether to use CSS modules
    additional_options: List[str] = [""]  # Default options
    prompt: str = "Create a responsive screen component with proper state management"  # Default prompt
    
    class Config:
        # Add validation schema for allowed values
        schema_extra = {
            "example": {
                "framework": "react",
                "language": "typescript",
                "styling": "tailwindcss",
                "screen_name": "Dashboard",
                "state_management": "redux",
                "bundler": "vite",
                "routing": "react-router-dom",
                "testing": "vitest",
                "linting": "eslint",
                "css_preprocessor": "scss",
                "css_modules": True,
                "prompt": "Create a dashboard screen with data visualization components"
            }
        }
