from typing import Optional, Union
from pydantic import BaseModel, Field


class ProjectCreateRequest(BaseModel):
    name: str = Field(..., description="Name of the project")
    description: Optional[str] = Field(None, description="Project description")
    visibility: str = Field(
        "private", description="Project visibility (private, internal, public)"
    )
    initialize_with_readme: bool = Field(
        True, description="Whether to initialize with README"
    )


class CodePushRequest(BaseModel):
    project_id: str = Field(..., description="ID or path of the GitLab project")
    local_path: str = Field(
        ..., description="Path to the local directory containing the code"
    )
    branch: str = Field("main", description="Branch name to push to")
    commit_message: str = Field(
        "Code pushed from WorkFlow.AI", description="Commit message"
    )
    create_branch: bool = Field(
        True, description="Whether to create the branch if it doesn't exist"
    )


class CodePullRequest(BaseModel):
    project_id: Union[int, str] = Field(..., description="ID or path of the GitLab project")
    branch: str = Field(..., description="Branch name to pull from")
    local_path: str = Field(..., description="Local directory path to pull the code into")
