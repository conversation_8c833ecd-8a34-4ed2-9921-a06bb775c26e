from typing import List, Optional, Any, Dict
from pydantic import BaseModel, Field


class AddApiRequest(BaseModel):
    """Request model for adding a new API using a natural language prompt."""

    prompt: str = Field(
        ...,
        description="Natural language prompt describing the API to create.",
        example="Create an API for adding a new employee in the employees table in typescript - full_name, gender, address, email_address, contact, salary, age, doj",
    )

class SearchApiRequest(BaseModel):
    """Request model for generating a search API based on a natural language prompt."""
    prompt: str = Field(
        ..., 
        description="Natural language prompt describing the search API to create.",
        example="Create a search API for employees who have completed 1 year in the IT department"
    )

class ListApiRequest(BaseModel):
    """Request model for generating a list API based on a natural language prompt."""
    prompt: str = Field(
        ..., 
        description="Natural language prompt describing the list API to create.",
        example="Create a list API for employees with sorting by name, department and date of joining"
    )

class FetchApiRequest(BaseModel):
    """Request model for generating a fetch/get API endpoint."""
    prompt: str = Field(
        ..., 
        description="Natural language prompt describing the fetch API to create. The prompt should specify the resource name and any specific requirements.",
        example="Create a fetch API for the employee resource in typescript and mysql that retrieves details of a single employee by ID."
    )

class DeleteApiRequest(BaseModel):
    """Request model for generating a delete API endpoint."""
    prompt: str = Field(
        ..., 
        description="Natural language prompt describing the delete API to create with specific resource details.",
        example= "Create an API for deleting an employee by id in the employees table in typescript & mysql"

    )

