import os
import tempfile
import shutil
import git
from pathlib import Path
from typing import Dict, List, Optional, Union
from github import Github, GithubException

from app.core.config import get_settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class GitHubException(Exception):
    """Exception raised for GitHub related errors."""

    pass


class GitHubService:
    """Service for GitHub operations."""

    def __init__(self, token: Optional[str] = None, url: Optional[str] = None):
        """
        Initialize GitHub service with credentials.

        Args:
            token: GitHub personal access token
            url: GitHub instance URL (for GitHub Enterprise)
        """
        settings = get_settings()
        self.token = token or settings.GITHUB_TOKEN
        self.url = url or settings.GITHUB_URL

        if not self.token:
            logger.error("GitHub token is not provided")
            raise GitHubException("GitHub token is required but not provided")

        try:
            logger.info(f"Initializing GitHub client")
            if self.url and self.url != "https://github.com":
                # For GitHub Enterprise
                self.github_client = Github(
                    base_url=f"{self.url}/api/v3", login_or_token=self.token
                )
            else:
                self.github_client = Github(self.token)

            # Test authentication
            self.github_client.get_user().login
            logger.info("Successfully authenticated with GitHub")
        except Exception as e:
            logger.error(f"Failed to authenticate with GitHub: {str(e)}")
            raise GitHubException(f"Authentication with GitHub failed: {str(e)}")

    def create_project(
        self,
        name: str,
        description: Optional[str] = None,
        visibility: str = "private",
        initialize_with_readme: bool = True,
    ) -> Dict:
        """
        Create a new GitHub repository.

        Args:
            name: Name of the repository
            description: Repository description
            visibility: Repository visibility (private or public)
            initialize_with_readme: Whether to initialize with README

        Returns:
            Dict containing repository information
        """
        try:
            logger.info(f"Creating GitHub repository: {name}")

            # Validate inputs
            if visibility not in ["private", "public"]:
                logger.warning(
                    f"Invalid visibility '{visibility}', defaulting to 'private'"
                )
                visibility = "private"

            # Convert visibility to boolean (private = True)
            is_private = visibility == "private"

            # Create repository
            user = self.github_client.get_user()
            repo = user.create_repo(
                name=name,
                description=description or f"Repository {name} created via API",
                private=is_private,
                auto_init=initialize_with_readme,
            )

            logger.info(f"Successfully created repository '{name}'")

            return {
                "id": repo.id,
                "name": repo.name,
                "description": repo.description,
                "web_url": repo.html_url,
                "ssh_url_to_repo": repo.ssh_url,
                "http_url_to_repo": repo.clone_url,
                "visibility": "private" if repo.private else "public",
            }

        except GithubException as e:
            logger.error(f"Failed to create repository '{name}': {str(e)}")
            raise GitHubException(f"Failed to create repository: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating repository '{name}': {str(e)}")
            raise GitHubException(
                f"Unexpected error while creating repository: {str(e)}"
            )

    def list_projects(self, search: Optional[str] = None, limit: int = 5) -> List[Dict]:
        """
        List GitHub repositories owned by the current user.

        Args:
            search: Search term for repositories
            limit: Maximum number of repositories to return

        Returns:
            List of repositories with their details
        """
        try:
            logger.info(f"Listing owned GitHub repositories with search term: {search}")

            # Get user
            user = self.github_client.get_user()

            # Filter repositories
            repos = user.get_repos(type="owner")

            # Apply search filter if provided
            result = []
            count = 0

            for repo in repos:
                if count >= limit:
                    break

                if search and search.lower() not in repo.name.lower():
                    continue

                result.append(
                    {
                        "id": repo.id,
                        "name": repo.name,
                        "path_with_namespace": f"{user.login}/{repo.name}",
                        "description": repo.description,
                        "web_url": repo.html_url,
                        "ssh_url_to_repo": repo.ssh_url,
                        "http_url_to_repo": repo.clone_url,
                        "visibility": "private" if repo.private else "public",
                    }
                )
                count += 1

            logger.info(f"Successfully listed {len(result)} owned GitHub repositories")
            return result

        except GithubException as e:
            logger.error(f"Failed to list GitHub repositories: {str(e)}")
            raise GitHubException(f"Failed to list GitHub repositories: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing GitHub repositories: {str(e)}")
            raise GitHubException(
                f"Unexpected error while listing repositories: {str(e)}"
            )

    def get_project_branches(self, project_id: Union[int, str]) -> List[Dict]:
        """
        Get branches for a GitHub repository.

        Args:
            project_id: Name or ID of the GitHub repository

        Returns:
            List of branches with their details
        """
        try:
            logger.info(f"Getting branches for GitHub repository {project_id}")

            # Get repository
            repo = self._get_repo(project_id)

            # Get branches
            branches = repo.get_branches()

            # Format response
            result = []
            for branch in branches:
                commit = branch.commit
                result.append(
                    {
                        "name": branch.name,
                        "protected": branch.protected,
                        "commit": {
                            "id": commit.sha,
                            "short_id": commit.sha[:8],
                            "message": commit.commit.message,
                        },
                        "default": branch.name == repo.default_branch,
                    }
                )

            logger.info(
                f"Successfully retrieved {len(result)} branches for repository {project_id}"
            )
            return result

        except GithubException as e:
            logger.error(
                f"Failed to get branches for repository {project_id}: {str(e)}"
            )
            raise GitHubException(
                f"Failed to get branches for repository {project_id}: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting branches: {str(e)}")
            raise GitHubException(f"Unexpected error while getting branches: {str(e)}")

    def push_code(
        self,
        project_id: Union[int, str],
        local_path: str,
        branch: str = "main",
        commit_message: str = "Code pushed from WorkFlow.AI",
        create_branch: bool = True,
    ) -> Dict:
        """
        Advanced push code to a GitHub repository with automatic branch handling.

        Args:
            project_id: Name or ID of the GitHub repository
            local_path: Path to the local directory containing the code
            branch: Branch name to push to
            commit_message: Commit message
            create_branch: Whether to create the branch if it doesn't exist

        Returns:
            Dict containing information about the push operation
        """
        temp_dir = None
        try:
            logger.info(f"Push to repository {project_id} on branch '{branch}'")

            # Verify repository exists
            repo = self._get_repo(project_id)

            # Create a temporary directory for git operations
            temp_dir = tempfile.mkdtemp()

            # Clone the repository
            repo_url = repo.clone_url.replace("https://", f"https://{self.token}@")
            logger.info(f"Cloning repository from {repo.clone_url}")

            try:
                git_repo = git.Repo.clone_from(repo_url, temp_dir)

                # Get default branch
                default_branch = repo.default_branch
                logger.info(f"Default branch for repository: {default_branch}")

                # Check if any branches exist by examining the refs
                refs = list(git_repo.refs)
                has_branches = (
                    any(not ref.name.startswith("HEAD") for ref in refs)
                    if refs
                    else False
                )

                # Check if the specified branch exists
                branch_exists = False
                for ref in git_repo.refs:
                    if ref.name == f"origin/{branch}":
                        branch_exists = True
                        break

                if has_branches:
                    # Repository has branches, try to checkout the default branch
                    if default_branch:
                        try:
                            logger.info(
                                f"Checking out default branch: {default_branch}"
                            )
                            git_repo.git.checkout(default_branch)
                        except git.GitCommandError as e:
                            logger.warning(
                                f"Could not checkout default branch: {str(e)}"
                            )
                            # Try to checkout any available branch
                            for ref in git_repo.refs:
                                if not ref.name.startswith("HEAD"):
                                    try:
                                        branch_name = ref.name.replace("origin/", "")
                                        logger.info(
                                            f"Trying to checkout branch: {branch_name}"
                                        )
                                        git_repo.git.checkout(branch_name)
                                        break
                                    except git.GitCommandError:
                                        continue

                    # If requested branch doesn't exist, create it
                    if not branch_exists and create_branch:
                        logger.info(f"Creating new branch: {branch}")
                        git_repo.git.checkout("-b", branch)
                    elif branch_exists:
                        logger.info(f"Checking out existing branch: {branch}")
                        git_repo.git.checkout(branch)
                else:
                    # This is a blank repository without any branches
                    logger.info("This is a blank repository without any branches")
                    # Create an initial commit to establish the target branch
                    # Create a README file
                    readme_path = Path(temp_dir) / "README.md"
                    with open(readme_path, "w") as f:
                        f.write(f"# {repo.name}\n\nInitialized by WorkFlow.AI")

                    # Initialize git repository since we're starting from scratch
                    if not os.path.exists(os.path.join(temp_dir, ".git")):
                        logger.info("Initializing git repository")
                        git_repo = git.Repo.init(temp_dir)

                    # Configure git
                    git_repo.git.config("user.email", "<EMAIL>")
                    git_repo.git.config("user.name", "WorkFlow.AI")

                    # Add remote
                    try:
                        git_repo.create_remote("origin", repo_url)
                    except git.GitCommandError:
                        # Remote might already exist
                        pass

                    # Add README and commit
                    git_repo.git.add("README.md")
                    git_repo.git.commit("-m", "Initial commit")

                    # Create the branch locally
                    logger.info(f"Creating initial branch: {branch}")
                    try:
                        # Try checking out - if already on that branch, this is fine
                        current_branch = git_repo.active_branch.name
                        if current_branch != branch:
                            git_repo.git.checkout("-b", branch)
                    except (git.GitCommandError, TypeError) as e:
                        # TypeError can occur if there's no active branch yet
                        logger.info(f"Creating branch {branch} directly: {str(e)}")
                        git_repo.git.checkout("-b", branch)

                # Copy files from local_path to temp_dir
                source_path = Path(local_path)
                if not source_path.exists():
                    raise GitHubException(f"Source path {local_path} does not exist")

                # Copy all files from source to temp directory
                for item in source_path.glob("**/*"):
                    if item.is_file():
                        # Create relative path
                        rel_path = item.relative_to(source_path)
                        # Create target path in temp directory
                        target_path = Path(temp_dir) / rel_path
                        # Create parent directories if they don't exist
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        # Copy the file
                        shutil.copy2(item, target_path)

                # Add all files to git
                git_repo.git.add(".")

                # Check if there are changes to commit
                if git_repo.is_dirty() or len(git_repo.untracked_files) > 0:
                    # Commit the changes
                    git_repo.git.config("user.email", "<EMAIL>")
                    git_repo.git.config("user.name", "WorkFlow.AI")
                    git_repo.git.commit("-m", commit_message)

                    # Push the changes
                    logger.info(
                        f"Pushing changes to remote repository on branch '{branch}'"
                    )
                    git_repo.git.push("--set-upstream", "origin", branch)

                    commit = git_repo.head.commit
                    logger.info(
                        f"Successfully pushed code with commit: {commit.hexsha}"
                    )

                    return {
                        "project_id": project_id,
                        "project_name": repo.name,
                        "branch": branch,
                        "branch_created": not branch_exists,
                        "commit_message": commit_message,
                        "commit_id": commit.hexsha,
                        "web_url": f"{repo.html_url}/commit/{commit.hexsha}",
                        "status": "success",
                    }
                else:
                    logger.info("No changes to commit")
                    return {
                        "project_id": project_id,
                        "project_name": repo.name,
                        "branch": branch,
                        "branch_created": not branch_exists,
                        "status": "no_changes",
                    }

            except git.GitCommandError as e:
                if (
                    "repository not found" in str(e).lower()
                    or "empty repository" in str(e).lower()
                ):
                    # This is a brand new empty repository
                    logger.info("Detected empty repository. Setting up from scratch.")

                    # Initialize a new git repository
                    os.system(f"rm -rf {temp_dir}/*")  # Clean the temp dir
                    git_repo = git.Repo.init(temp_dir)

                    # Configure git
                    git_repo.git.config("user.email", "<EMAIL>")
                    git_repo.git.config("user.name", "WorkFlow.AI")

                    # Create a README file
                    readme_path = Path(temp_dir) / "README.md"
                    with open(readme_path, "w") as f:
                        f.write(f"# {repo.name}\n\nInitialized by WorkFlow.AI")

                    # Add remote
                    git_repo.create_remote("origin", repo_url)

                    # Add README and initial commit
                    git_repo.git.add(".")
                    git_repo.git.commit("-m", "Initial commit")

                    # Create branch
                    logger.info(f"Creating initial branch: {branch}")
                    git_repo.git.checkout("-b", branch)

                    # Copy files from local_path to temp_dir
                    source_path = Path(local_path)
                    if not source_path.exists():
                        raise GitHubException(
                            f"Source path {local_path} does not exist"
                        )

                    # Copy all files from source to temp directory
                    for item in source_path.glob("**/*"):
                        if item.is_file():
                            # Create relative path
                            rel_path = item.relative_to(source_path)
                            # Create target path in temp directory
                            target_path = Path(temp_dir) / rel_path
                            # Create parent directories if they don't exist
                            target_path.parent.mkdir(parents=True, exist_ok=True)
                            # Copy the file
                            shutil.copy2(item, target_path)

                    # Add all changes
                    git_repo.git.add(".")

                    # Check if there are changes to commit
                    if git_repo.is_dirty() or len(git_repo.untracked_files) > 0:
                        # Commit the changes
                        git_repo.git.commit("-m", commit_message)

                        # Push the changes
                        logger.info(
                            f"Pushing changes to remote repository on branch '{branch}'"
                        )
                        git_repo.git.push("--set-upstream", "origin", branch)

                        commit = git_repo.head.commit
                        logger.info(
                            f"Successfully pushed code with commit: {commit.hexsha}"
                        )

                        return {
                            "project_id": project_id,
                            "project_name": repo.name,
                            "branch": branch,
                            "branch_created": True,
                            "commit_message": commit_message,
                            "commit_id": commit.hexsha,
                            "web_url": f"{repo.html_url}/commit/{commit.hexsha}",
                            "status": "success",
                        }
                    else:
                        # Push the initial commit at least
                        git_repo.git.push("--set-upstream", "origin", branch)

                        commit = git_repo.head.commit
                        logger.info(f"Pushed initial commit: {commit.hexsha}")

                        return {
                            "project_id": project_id,
                            "project_name": repo.name,
                            "branch": branch,
                            "branch_created": True,
                            "commit_message": "Initial commit",
                            "commit_id": commit.hexsha,
                            "web_url": f"{repo.html_url}/commit/{commit.hexsha}",
                            "status": "success",
                        }
                else:
                    # Re-raise the error if it's not related to an empty repository
                    raise

        except git.GitCommandError as e:
            logger.error(f"Git command error: {str(e)}")
            raise GitHubException(f"Git command error: {str(e)}")
        except GithubException as e:
            logger.error(f"Failed to get repository {project_id}: {str(e)}")
            raise GitHubException(f"Failed to get repository {project_id}: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during advanced code push: {str(e)}")
            raise GitHubException(
                f"Unexpected error during advanced code push: {str(e)}"
            )
        finally:
            # Clean up the temporary directory
            if temp_dir and os.path.exists(temp_dir):
                logger.debug(f"Cleaning up temporary directory: {temp_dir}")
                shutil.rmtree(temp_dir)

    def _get_repo(self, project_id: Union[int, str]):
        """
        Helper method to get a GitHub repository by ID or name.

        If project_id is numeric, it treats it as an ID; otherwise, as a full repo name.
        For string values, it can be formatted as "owner/repo" or just "repo" (in which case
        the user's own repository will be returned).
        """
        try:
            if isinstance(project_id, int) or project_id.isdigit():
                # Find repo by ID
                user = self.github_client.get_user()
                for repo in user.get_repos():
                    if str(repo.id) == str(project_id):
                        return repo
                raise GitHubException(f"Repository with ID {project_id} not found")
            else:
                # If in the format 'owner/repo'
                if "/" in project_id:
                    return self.github_client.get_repo(project_id)
                else:
                    # Assume it's a repo owned by the authenticated user
                    user = self.github_client.get_user()
                    return user.get_repo(project_id)
        except GithubException as e:
            logger.error(f"Failed to get repository {project_id}: {str(e)}")
            raise GitHubException(f"Failed to get repository {project_id}: {str(e)}")

    def pull_code(
        self, project_id: Union[int, str], branch: str, local_path: str
    ) -> Dict:
        """
        Pull latest changes from a GitHub repository branch into a local directory.
        Clones the repo if the local path doesn't exist or isn't a git repo.

        Args:
            project_id: Name or ID of the GitHub repository
            branch: Branch name to pull from
            local_path: Local directory path to pull the code into

        Returns:
            Dict containing information about the pull operation
        """
        repo = None
        local_path_obj = Path(local_path)

        try:
            logger.info(
                f"Pulling code for repository {project_id}, branch '{branch}' into {local_path}"
            )

            # Verify repository exists
            github_repo = self._get_repo(project_id)
            repo_url = github_repo.clone_url.replace(
                "https://", f"https://{self.token}@"
            )

            # Check if local_path exists and is a valid git repo
            is_repo = False
            if local_path_obj.exists() and local_path_obj.is_dir():
                try:
                    repo = git.Repo(local_path)
                    # Check if it's the correct remote
                    if (
                        "origin" in repo.remotes
                        and repo.remotes.origin.url.split("@")[-1]
                        == repo_url.split("@")[-1]
                    ):
                        is_repo = True
                    else:
                        logger.warning(
                            f"Directory {local_path} exists but is not the correct GitHub repository or remote 'origin' is misconfigured. Re-cloning."
                        )
                        # Clean the directory before cloning
                        shutil.rmtree(local_path)
                        local_path_obj.mkdir(
                            parents=True, exist_ok=True
                        )  # Recreate dir
                        is_repo = False
                except git.InvalidGitRepositoryError:
                    logger.warning(
                        f"Directory {local_path} exists but is not a valid git repository. Re-cloning."
                    )
                    # Clean the directory before cloning
                    shutil.rmtree(local_path)
                    local_path_obj.mkdir(parents=True, exist_ok=True)  # Recreate dir
                    is_repo = False
                except Exception as e:
                    logger.error(
                        f"Error checking existing repository at {local_path}: {str(e)}"
                    )
                    raise GitHubException(f"Error checking local path: {str(e)}")

            if is_repo and repo:
                # Existing repo - fetch, checkout, and pull
                logger.info(
                    f"Found existing repository at {local_path}. Fetching updates."
                )
                origin = repo.remotes.origin
                origin.fetch()

                # Check if branch exists remotely
                remote_branch_exists = False
                for ref in origin.refs:
                    if ref.name == f"origin/{branch}":
                        remote_branch_exists = True
                        break

                if not remote_branch_exists:
                    raise GitHubException(
                        f"Branch '{branch}' does not exist in the remote repository."
                    )

                # Checkout the branch
                try:
                    logger.info(f"Checking out branch '{branch}'")
                    repo.git.checkout(branch)
                except git.GitCommandError as e:
                    # If checkout fails, maybe the local branch doesn't exist yet but remote does
                    if f"pathspec '{branch}' did not match" in str(e):
                        logger.info(
                            f"Local branch '{branch}' not found, creating and tracking remote."
                        )
                        repo.git.checkout("-b", branch, f"origin/{branch}")
                    else:
                        raise  # Re-raise other checkout errors

                # Pull latest changes
                logger.info(f"Pulling latest changes for branch '{branch}'")
                pull_info = origin.pull(branch)

                # Check pull status (GitPython pull returns a list of FetchInfo objects)
                # We can check flags in the FetchInfo objects
                status = "updated"
                if any(info.flags & info.ERROR for info in pull_info):
                    status = "error_during_pull"
                elif any(info.flags & info.REJECTED for info in pull_info):
                    status = "rejected"  # e.g. merge conflicts
                elif not any(
                    info.flags & (info.NEW_HEAD | info.FAST_FORWARD)
                    for info in pull_info
                ):
                    # If no flags indicate an update, it was already up-to-date
                    status = "already_up_to_date"

                commit = repo.head.commit
                logger.info(
                    f"Successfully pulled updates for branch '{branch}'. Current commit: {commit.hexsha}"
                )
                return {
                    "project_id": project_id,
                    "project_name": github_repo.name,
                    "branch": branch,
                    "local_path": local_path,
                    "commit_id": commit.hexsha,
                    "status": status,
                }

            else:
                # Directory doesn't exist or wasn't a valid repo - clone
                logger.info(
                    f"Cloning repository {github_repo.clone_url} into {local_path}"
                )
                # Ensure parent directory exists
                local_path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Clone the specific branch
                try:
                    repo = git.Repo.clone_from(repo_url, local_path, branch=branch)
                    status = "cloned"
                except git.GitCommandError as e:
                    # Handle case where branch might not exist on clone
                    if f"Remote branch {branch} not found" in str(e):
                        raise GitHubException(
                            f"Branch '{branch}' does not exist in the remote repository."
                        )
                    else:
                        raise  # Re-raise other clone errors

                commit = repo.head.commit
                logger.info(
                    f"Successfully cloned branch '{branch}'. Current commit: {commit.hexsha}"
                )
                return {
                    "project_id": project_id,
                    "project_name": github_repo.name,
                    "branch": branch,
                    "local_path": local_path,
                    "commit_id": commit.hexsha,
                    "status": status,
                }

        except git.GitCommandError as e:
            logger.error(f"Git command error during pull/clone: {str(e)}")
            raise GitHubException(f"Git command error: {str(e)}")
        except GithubException as e:
            logger.error(f"Failed to get repository {project_id}: {str(e)}")
            raise GitHubException(f"Failed to get repository {project_id}: {str(e)}")
        except GitHubException as e:  # Re-raise specific GitHub exceptions
            raise e
        except Exception as e:
            logger.error(f"Unexpected error during code pull: {str(e)}")
            raise GitHubException(f"Unexpected error during code pull: {str(e)}")
