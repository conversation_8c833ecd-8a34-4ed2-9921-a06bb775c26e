import os
import gitlab
import tempfile
import shutil
import git
from pathlib import Path
from typing import Dict, List, Optional, Union

from app.core.config import get_settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class GitLabException(Exception):
    """Exception raised for GitLab related errors."""

    pass


class GitLabService:
    """Service for GitLab operations."""

    def __init__(self, token: Optional[str] = None, url: Optional[str] = None):
        """
        Initialize GitLab service with credentials.

        Args:
            token: GitLab personal access token
            url: GitLab instance URL
        """
        settings = get_settings()
        self.token = token or settings.GITLAB_TOKEN
        self.url = url or settings.GITLAB_URL

        if not self.token:
            logger.error("GitLab token is not provided")
            raise GitLabException("GitLab token is required but not provided")

        try:
            logger.info(f"Initializing GitLab client for {self.url}")
            self.gitlab_client = gitlab.Gitlab(self.url, private_token=self.token)
            self.gitlab_client.auth()
            logger.info("Successfully authenticated with GitLab")
        except gitlab.exceptions.GitlabAuthenticationError as e:
            logger.error(f"Failed to authenticate with GitLab: {str(e)}")
            raise GitLabException(f"Authentication with GitLab failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing GitLab client: {str(e)}")
            raise GitLabException(f"Failed to initialize GitLab client: {str(e)}")

    def create_project(
        self,
        name: str,
        description: Optional[str] = None,
        visibility: str = "private",
        initialize_with_readme: bool = True,
    ) -> Dict:
        """
        Create a new GitLab project/repository.

        Args:
            name: Name of the project
            description: Project description
            visibility: Project visibility (private, internal, public)
            initialize_with_readme: Whether to initialize with README

        Returns:
            Dict containing project information
        """
        try:
            logger.info(f"Creating GitLab project: {name}")

            # Validate inputs
            if visibility not in ["private", "internal", "public"]:
                logger.warning(
                    f"Invalid visibility '{visibility}', defaulting to 'private'"
                )
                visibility = "private"

            # Create project
            project = self.gitlab_client.projects.create(
                {
                    "name": name,
                    "description": description or f"Project {name} created via API",
                    "visibility": visibility,
                    "initialize_with_readme": initialize_with_readme,
                }
            )

            logger.info(f"Successfully created project '{name}' with ID {project.id}")

            return {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "web_url": project.web_url,
                "ssh_url_to_repo": project.ssh_url_to_repo,
                "http_url_to_repo": project.http_url_to_repo,
                "visibility": project.visibility,
            }

        except gitlab.exceptions.GitlabCreateError as e:
            logger.error(f"Failed to create project '{name}': {str(e)}")
            raise GitLabException(f"Failed to create project: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating project '{name}': {str(e)}")
            raise GitLabException(f"Unexpected error while creating project: {str(e)}")

    def list_projects(self, search: Optional[str] = None, limit: int = 5) -> List[Dict]:
        """
        List GitLab projects owned by the current user.

        Args:
            search: Search term for projects
            limit: Maximum number of projects to return

        Returns:
            List of projects with their details
        """
        try:
            logger.info(f"Listing owned GitLab projects with search term: {search}")

            # Build query parameters - add owned=True to only show owned projects
            params = {"owned": True, "per_page": limit}
            if search:
                params["search"] = search

            # Fetch projects
            projects_list = self.gitlab_client.projects.list(**params)

            # Format response
            result = []
            for project_summary in projects_list:
                try:
                    # Get full project details to access all attributes
                    project = self.gitlab_client.projects.get(project_summary.id)

                    # Add project details to result
                    result.append(
                        {
                            "id": project.id,
                            "name": project.name,
                            "path_with_namespace": project.path_with_namespace,
                            "description": project.description,
                            "web_url": project.web_url,
                            "ssh_url_to_repo": project.ssh_url_to_repo,
                            "http_url_to_repo": project.http_url_to_repo,
                            "visibility": project.visibility,
                        }
                    )
                except gitlab.exceptions.GitlabGetError as e:
                    logger.warning(
                        f"Failed to get details for project ID {project_summary.id}: {str(e)}"
                    )
                    # Include basic information that is available from the list response
                    result.append(
                        {
                            "id": project_summary.id,
                            "name": project_summary.name,
                            "path_with_namespace": project_summary.path_with_namespace,
                            "web_url": getattr(project_summary, "web_url", "N/A"),
                            "description": getattr(
                                project_summary, "description", "N/A"
                            ),
                        }
                    )

            logger.info(f"Successfully listed {len(result)} owned GitLab projects")
            return result

        except gitlab.exceptions.GitlabListError as e:
            logger.error(f"Failed to list GitLab projects: {str(e)}")
            raise GitLabException(f"Failed to list GitLab projects: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing GitLab projects: {str(e)}")
            raise GitLabException(f"Unexpected error while listing projects: {str(e)}")

    def get_project_branches(self, project_id: Union[int, str]) -> List[Dict]:
        """
        Get branches for a GitLab project.

        Args:
            project_id: ID or path of the GitLab project

        Returns:
            List of branches with their details
        """
        try:
            logger.info(f"Getting branches for GitLab project ID {project_id}")

            # Get project
            project = self.gitlab_client.projects.get(project_id)

            # Get branches
            branches = project.branches.list(all=True)

            # Format response
            result = []
            for branch in branches:
                result.append(
                    {
                        "name": branch.name,
                        "default": branch.default,
                        "protected": branch.protected,
                        "merged": branch.merged,
                        "commit": {
                            "id": branch.commit.get("id"),
                            "short_id": branch.commit.get("short_id"),
                            "title": branch.commit.get("title"),
                            "created_at": branch.commit.get("created_at"),
                        },
                    }
                )

            logger.info(
                f"Successfully retrieved {len(result)} branches for project ID {project_id}"
            )
            return result

        except gitlab.exceptions.GitlabGetError as e:
            logger.error(
                f"Failed to get branches for project ID {project_id}: {str(e)}"
            )
            raise GitLabException(
                f"Failed to get branches for project ID {project_id}: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting branches: {str(e)}")
            raise GitLabException(f"Unexpected error while getting branches: {str(e)}")

    def push_code(
        self,
        project_id: Union[int, str],
        local_path: str,
        branch: str = "main",
        commit_message: str = "Code pushed from WorkFlow.AI",
        create_branch: bool = True,
    ) -> Dict:
        """
        Advanced push code to a GitLab repository with automatic branch handling.

        Args:
            project_id: ID or path of the GitLab project
            local_path: Path to the local directory containing the code
            branch: Branch name to push to
            commit_message: Commit message
            create_branch: Whether to create the branch if it doesn't exist

        Returns:
            Dict containing information about the push operation
        """
        temp_dir = None
        try:
            logger.info(
                f"Push to project ID {project_id} on branch '{branch}'"
            )

            # Verify project exists
            project = self.gitlab_client.projects.get(project_id)

            # Create a temporary directory for git operations
            temp_dir = tempfile.mkdtemp()

            # Clone the repository
            repo_url = project.http_url_to_repo.replace(
                "://", f"://oauth2:{self.token}@"
            )
            logger.info(f"Cloning repository from {project.http_url_to_repo}")

            try:
                repo = git.Repo.clone_from(repo_url, temp_dir)

                # Get default branch if exists
                default_branch = project.default_branch
                logger.info(f"Default branch for project: {default_branch}")

                # Check if any branches exist by examining the refs
                refs = list(repo.refs)
                has_branches = (
                    any(not ref.name.startswith("HEAD") for ref in refs)
                    if refs
                    else False
                )

                # Check if the specified branch exists
                branch_exists = False
                for ref in repo.refs:
                    if ref.name == f"origin/{branch}":
                        branch_exists = True
                        break

                if has_branches:
                    # Repository has branches, try to checkout the default branch
                    if default_branch:
                        try:
                            logger.info(
                                f"Checking out default branch: {default_branch}"
                            )
                            repo.git.checkout(default_branch)
                        except git.GitCommandError as e:
                            logger.warning(
                                f"Could not checkout default branch: {str(e)}"
                            )
                            # Try to checkout any available branch
                            for ref in repo.refs:
                                if not ref.name.startswith("HEAD"):
                                    try:
                                        branch_name = ref.name.replace("origin/", "")
                                        logger.info(
                                            f"Trying to checkout branch: {branch_name}"
                                        )
                                        repo.git.checkout(branch_name)
                                        break
                                    except git.GitCommandError:
                                        continue
                else:
                    # This is a blank repository without any branches
                    logger.info("This is a blank repository without any branches")
                    # Create an initial commit to establish the target branch
                    # Create a README file
                    readme_path = Path(temp_dir) / "README.md"
                    with open(readme_path, "w") as f:
                        f.write(f"# {project.name}\n\nInitialized by WorkFlow.AI")

                    # Initialize git repository since we're starting from scratch
                    if not os.path.exists(os.path.join(temp_dir, ".git")):
                        logger.info("Initializing git repository")
                        repo = git.Repo.init(temp_dir)

                    # Configure git
                    repo.git.config("user.email", "<EMAIL>")
                    repo.git.config("user.name", "WorkFlow.AI")

                    # Add remote
                    try:
                        repo.create_remote("origin", repo_url)
                    except git.GitCommandError:
                        # Remote might already exist
                        pass

                    # Add README and commit
                    repo.git.add("README.md")
                    repo.git.commit("-m", "Initial commit")

                    # Create the branch locally
                    logger.info(f"Creating initial branch: {branch}")
                    try:
                        # Try checking out - if already on that branch, this is fine
                        current_branch = repo.active_branch.name
                        if current_branch != branch:
                            repo.git.checkout("-b", branch)
                    except (git.GitCommandError, TypeError) as e:
                        # TypeError can occur if there's no active branch yet
                        logger.info(f"Creating branch {branch} directly: {str(e)}")
                        repo.git.checkout("-b", branch)

                # At this point, we should either be on an existing branch or have created an initial branch

                # Now create the target branch if it doesn't exist and it's different from the current branch
                if not branch_exists and branch != repo.active_branch.name:
                    if not create_branch:
                        raise GitLabException(
                            f"Branch '{branch}' does not exist and create_branch is set to False"
                        )
                    logger.info(f"Creating new branch: {branch}")
                    repo.git.checkout("-b", branch)
                elif branch_exists and branch != repo.active_branch.name:
                    logger.info(f"Checking out existing branch: {branch}")
                    repo.git.checkout(branch)

                # Copy files from local_path to temp_dir
                source_path = Path(local_path)
                if not source_path.exists():
                    raise GitLabException(f"Source path {local_path} does not exist")

                # Clear the temp directory except .git
                for item in Path(temp_dir).iterdir():
                    if item.name != ".git":
                        if item.is_dir():
                            shutil.rmtree(item)
                        else:
                            item.unlink()

                # Copy all files from source to temp directory
                if source_path.is_dir():
                    for item in source_path.glob("**/*"):
                        if item.is_dir():
                            continue
                        rel_path = item.relative_to(source_path)
                        dest_path = Path(temp_dir) / rel_path
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(item, dest_path)

                # Add files to git
                repo.git.add("--all")

                # Check if there are changes to commit
                if repo.is_dirty() or len(repo.untracked_files) > 0:
                    # Commit the changes
                    repo.git.config("user.email", "<EMAIL>")
                    repo.git.config("user.name", "WorkFlow.AI")
                    repo.git.commit("-m", commit_message)

                    # Push the changes
                    logger.info(
                        f"Pushing changes to remote repository on branch '{branch}'"
                    )
                    repo.git.push("--set-upstream", "origin", branch)

                    commit = repo.head.commit
                    logger.info(
                        f"Successfully pushed code with commit: {commit.hexsha}"
                    )

                    return {
                        "project_id": project_id,
                        "project_name": project.name,
                        "branch": branch,
                        "branch_created": not branch_exists,
                        "commit_message": commit_message,
                        "commit_id": commit.hexsha,
                        "web_url": f"{project.web_url}/commit/{commit.hexsha}",
                        "status": "success",
                    }
                else:
                    logger.info("No changes to commit")
                    return {
                        "project_id": project_id,
                        "project_name": project.name,
                        "branch": branch,
                        "branch_created": not branch_exists,
                        "status": "no_changes",
                    }

            except git.GitCommandError as e:
                if (
                    "repository not found" in str(e).lower()
                    or "empty repository" in str(e).lower()
                ):
                    # This is a brand new empty repository
                    logger.info("Detected empty repository. Setting up from scratch.")

                    # Initialize a new git repository
                    os.system(f"rm -rf {temp_dir}/*")  # Clean the temp dir
                    repo = git.Repo.init(temp_dir)

                    # Configure git
                    repo.git.config("user.email", "<EMAIL>")
                    repo.git.config("user.name", "WorkFlow.AI")

                    # Create a README file
                    readme_path = Path(temp_dir) / "README.md"
                    with open(readme_path, "w") as f:
                        f.write(f"# {project.name}\n\nInitialized by WorkFlow.AI")

                    # Add remote
                    repo.create_remote("origin", repo_url)

                    # Add README and initial commit
                    repo.git.add(".")
                    repo.git.commit("-m", "Initial commit")

                    # Create branch
                    logger.info(f"Creating initial branch: {branch}")
                    repo.git.checkout("-b", branch)

                    # Copy files from local_path to temp_dir
                    source_path = Path(local_path)
                    if not source_path.exists():
                        raise GitLabException(
                            f"Source path {local_path} does not exist"
                        )

                    # Copy all files from source to temp directory
                    if source_path.is_dir():
                        for item in source_path.glob("**/*"):
                            if item.is_dir():
                                continue
                            rel_path = item.relative_to(source_path)
                            dest_path = Path(temp_dir) / rel_path
                            dest_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(item, dest_path)

                    # Add files to git
                    repo.git.add("--all")

                    # Commit and push if there are changes
                    if repo.is_dirty() or len(repo.untracked_files) > 0:
                        repo.git.commit("-m", commit_message)

                        # Push the changes
                        logger.info(
                            f"Pushing initial changes to remote repository on branch '{branch}'"
                        )
                        repo.git.push("--set-upstream", "origin", branch)

                        commit = repo.head.commit
                        logger.info(
                            f"Successfully pushed code with commit: {commit.hexsha}"
                        )

                        return {
                            "project_id": project_id,
                            "project_name": project.name,
                            "branch": branch,
                            "branch_created": True,
                            "commit_message": commit_message,
                            "commit_id": commit.hexsha,
                            "web_url": f"{project.web_url}/commit/{commit.hexsha}",
                            "status": "success",
                        }
                    else:
                        # Push the initial commit at least
                        repo.git.push("--set-upstream", "origin", branch)

                        commit = repo.head.commit
                        logger.info(f"Pushed initial commit: {commit.hexsha}")

                        return {
                            "project_id": project_id,
                            "project_name": project.name,
                            "branch": branch,
                            "branch_created": True,
                            "commit_message": "Initial commit",
                            "commit_id": commit.hexsha,
                            "web_url": f"{project.web_url}/commit/{commit.hexsha}",
                            "status": "success",
                        }
                else:
                    # Re-raise the error if it's not related to an empty repository
                    raise

        except git.GitCommandError as e:
            logger.error(f"Git command error: {str(e)}")
            raise GitLabException(f"Git command error: {str(e)}")
        except gitlab.exceptions.GitlabGetError as e:
            logger.error(f"Failed to get project with ID {project_id}: {str(e)}")
            raise GitLabException(
                f"Failed to get project with ID {project_id}: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during advanced code push: {str(e)}")
            raise GitLabException(
                f"Unexpected error during advanced code push: {str(e)}"
            )
        finally:
            # Clean up the temporary directory
            if temp_dir and os.path.exists(temp_dir):
                logger.debug(f"Cleaning up temporary directory: {temp_dir}")
                shutil.rmtree(temp_dir)

    def pull_code(
        self, project_id: Union[int, str], branch: str, local_path: str
    ) -> Dict:
        """
        Pull latest changes from a GitLab repository branch into a local directory.
        Clones the repo if the local path doesn't exist or isn't a git repo.

        Args:
            project_id: ID or path of the GitLab project
            branch: Branch name to pull from
            local_path: Local directory path to pull the code into

        Returns:
            Dict containing information about the pull operation
        """
        repo = None
        local_path_obj = Path(local_path)

        try:
            logger.info(
                f"Pulling code for project ID {project_id}, branch '{branch}' into {local_path}"
            )

            # Verify project exists
            project = self.gitlab_client.projects.get(project_id)
            repo_url = project.http_url_to_repo.replace(
                "://", f"://oauth2:{self.token}@"
            )

            # Check if local_path exists and is a valid git repo
            is_repo = False
            if local_path_obj.exists() and local_path_obj.is_dir():
                try:
                    repo = git.Repo(local_path)
                    # Check if it's the correct remote
                    if (
                        "origin" in repo.remotes
                        and repo.remotes.origin.url.split("@")[-1]
                        == repo_url.split("@")[-1]
                    ):
                        is_repo = True
                    else:
                        logger.warning(
                            f"Directory {local_path} exists but is not the correct GitLab repository or remote 'origin' is misconfigured. Re-cloning."
                        )
                        # Clean the directory before cloning
                        shutil.rmtree(local_path)
                        local_path_obj.mkdir(parents=True, exist_ok=True) # Recreate dir
                        is_repo = False
                except git.InvalidGitRepositoryError:
                    logger.warning(
                        f"Directory {local_path} exists but is not a valid git repository. Re-cloning."
                    )
                    # Clean the directory before cloning
                    shutil.rmtree(local_path)
                    local_path_obj.mkdir(parents=True, exist_ok=True) # Recreate dir
                    is_repo = False
                except Exception as e:
                    logger.error(f"Error checking existing repository at {local_path}: {str(e)}")
                    raise GitLabException(f"Error checking local path: {str(e)}")


            if is_repo and repo:
                # Existing repo - fetch, checkout, and pull
                logger.info(f"Found existing repository at {local_path}. Fetching updates.")
                origin = repo.remotes.origin
                origin.fetch()

                # Check if branch exists remotely
                remote_branch_exists = False
                for ref in origin.refs:
                    if ref.name == f"origin/{branch}":
                        remote_branch_exists = True
                        break

                if not remote_branch_exists:
                     raise GitLabException(f"Branch '{branch}' does not exist in the remote repository.")

                # Checkout the branch
                try:
                    logger.info(f"Checking out branch '{branch}'")
                    repo.git.checkout(branch)
                except git.GitCommandError as e:
                     # If checkout fails, maybe the local branch doesn't exist yet but remote does
                     if f"pathspec '{branch}' did not match" in str(e):
                         logger.info(f"Local branch '{branch}' not found, creating and tracking remote.")
                         repo.git.checkout("-b", branch, f"origin/{branch}")
                     else:
                         raise # Re-raise other checkout errors

                # Pull latest changes
                logger.info(f"Pulling latest changes for branch '{branch}'")
                pull_info = origin.pull(branch)

                # Check pull status (GitPython pull returns a list of FetchInfo objects)
                # We can check flags in the FetchInfo objects
                status = "updated"
                if any(info.flags & info.ERROR for info in pull_info):
                    status = "error_during_pull"
                elif any(info.flags & info.REJECTED for info in pull_info):
                     status = "rejected" # e.g. merge conflicts
                elif not any(info.flags & (info.NEW_HEAD | info.FAST_FORWARD) for info in pull_info):
                     # If no flags indicate an update, it was already up-to-date
                     status = "already_up_to_date"


                commit = repo.head.commit
                logger.info(
                    f"Successfully pulled updates for branch '{branch}'. Current commit: {commit.hexsha}"
                )
                return {
                    "project_id": project_id,
                    "project_name": project.name,
                    "branch": branch,
                    "local_path": local_path,
                    "commit_id": commit.hexsha,
                    "status": status,
                }

            else:
                # Directory doesn't exist or wasn't a valid repo - clone
                logger.info(
                    f"Cloning repository {project.http_url_to_repo} into {local_path}"
                )
                # Ensure parent directory exists
                local_path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Clone the specific branch
                try:
                    repo = git.Repo.clone_from(
                        repo_url, local_path, branch=branch
                    )
                    status = "cloned"
                except git.GitCommandError as e:
                    # Handle case where branch might not exist on clone
                    if f"Remote branch {branch} not found" in str(e):
                         raise GitLabException(f"Branch '{branch}' does not exist in the remote repository.")
                    else:
                        raise # Re-raise other clone errors


                commit = repo.head.commit
                logger.info(
                    f"Successfully cloned branch '{branch}'. Current commit: {commit.hexsha}"
                )
                return {
                    "project_id": project_id,
                    "project_name": project.name,
                    "branch": branch,
                    "local_path": local_path,
                    "commit_id": commit.hexsha,
                    "status": status,
                }

        except git.GitCommandError as e:
            logger.error(f"Git command error during pull/clone: {str(e)}")
            raise GitLabException(f"Git command error: {str(e)}")
        except gitlab.exceptions.GitlabGetError as e:
            logger.error(f"Failed to get project with ID {project_id}: {str(e)}")
            raise GitLabException(
                f"Failed to get project with ID {project_id}: {str(e)}"
            )
        except GitLabException as e: # Re-raise specific GitLab exceptions
            raise e
        except Exception as e:
            logger.error(f"Unexpected error during code pull: {str(e)}")
            raise GitLabException(f"Unexpected error during code pull: {str(e)}")
