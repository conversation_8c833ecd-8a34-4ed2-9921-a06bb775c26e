import hashlib
import os
import re
import time
from pathlib import Path
from typing import Dict, Any, Tuple, List

from app.models.pydantic_models.fe_gen import BoilerplateRequest, CommonComponentRequest, ScreenRequest
from app.services.codegen.provider_factory import ProviderFactory
from app.utils.logger import get_logger
from app.templates.fe_template import FrontendTemplateManager

logger = get_logger(__name__)


class FrontendGenService:
    DEFAULT_FRAMEWORK = "react"
    DEFAULT_STYLING = "tailwindcss"
    DEFAULT_LANGUAGE = "typescript"
    DEFAULT_BUNDLER = "vite"
    DEFAULT_STATE_MANAGEMENT = "redux"
    DEFAULT_ROUTING = "react-router"

    FILE_WRITE_RETRIES = 3
    FILE_WRITE_RETRY_DELAY = 0.5  # seconds

    def __init__(self):
        self.template_manager = FrontendTemplateManager()
        self.provider_factory = ProviderFactory()

    async def generate_boilerplate(
        self,
        project_id: str,
        branch_id: str,
        request: BoilerplateRequest,
    ) -> Dict[str, Any]:
        logger.info(f"Generating boilerplate for {project_id}/{branch_id}")

        try:
            framework = request.framework or self.DEFAULT_FRAMEWORK
            styling = request.styling or self.DEFAULT_STYLING
            language = request.language or self.DEFAULT_LANGUAGE
            bundler = request.Bundler or self.DEFAULT_BUNDLER
            state_management = request.state_management or self.DEFAULT_STATE_MANAGEMENT
            routing = request.routing or self.DEFAULT_ROUTING

            # Update request object
            request.framework = framework
            request.styling = styling
            request.language = language
            request.Bundler = bundler
            request.state_management = state_management
            request.routing = routing

            # Get the template prompt
            template = self.template_manager.get_boilerplate_template(request)
            print(template)  # Debugging line to check the template
            # Generate code
            provider = await self.provider_factory.create_provider("ollama")
            generated_text = await provider.generate_code(
                prompt=template,
                language=language,
                temperature=0.2,
                max_tokens=6000,
                additional_params={
                    "num_ctx": 8192,
                    "top_k": 40,
                    "top_p": 0.9,
                    # "repeat_penalty": 1.1,
                },
            )

            # Extract and save files
            file_sections = self._extract_file_sections(generated_text)
            print(f"Extracted file sections: {file_sections}")  # Debugging line to check extracted files
            save_result = await self._save_fe_files(
                project_id, branch_id, file_sections
            )
            print(f"Save result: {save_result}")  # Debugging line to check save result

            return self._build_success_response(
                generation_type="boilerplate",
                framework=framework,
                styling=styling,
                language=language,
                generated_code=generated_text,
                file_sections=file_sections,
                save_result=save_result,
                base_dir=f"projects/{project_id}/branches/{branch_id}"
            )

        except Exception as e:
            logger.error(f"Boilerplate generation failed: {str(e)}", exc_info=True)
            return self._build_error_response("boilerplate", str(e))

    async def generate_common_component(
        self,
        project_id: str,
        branch_id: str,
        request: CommonComponentRequest,
    ) -> Dict[str, Any]:
        """Generate a common/reusable component based on the request."""
        logger.info(f"Generating common component for {project_id}/{branch_id}")

        try:
            # Get the template prompt
            template = self.template_manager.get_common_component_template(request)

            # Generate code
            provider = await self.provider_factory.create_provider("ollama")
            generated_text = await provider.generate_code(
                prompt=template,
                language=request.language,
                temperature=0.2,
                max_tokens=4000,
                additional_params={
                    "num_ctx": 6000,
                    "top_k": 40,
                    "top_p": 0.9,
                },
            )

            # Extract and save files
            file_sections = self._extract_file_sections(generated_text)
            save_result = await self._save_fe_files(
                project_id, branch_id, file_sections
            )

            return self._build_success_response(
                generation_type="component",
                framework=request.framework,
                styling=request.styling,
                language=request.language,
                generated_code=generated_text,
                file_sections=file_sections,
                save_result=save_result,
                base_dir=f"projects/{project_id}/branches/{branch_id}/components"
            )

        except Exception as e:
            logger.error(f"Component generation failed: {str(e)}", exc_info=True)
            return self._build_error_response("component", str(e))

    async def get_boilerplate_structure(self, project_id: str, branch_id: str) -> str:
        """
        Retrieve the folder structure of the project and branch as a string
        """
        base_dir = Path(f"projects/{project_id}/branches/{branch_id}")

        if not base_dir.exists():
            raise FileNotFoundError(f"Boilerplate directory not found for project_id: {project_id}, branch_id: {branch_id}")

        try:
            def build_structure(current_path: Path, parent_path=""):
                structure = []
                for entry in sorted(current_path.iterdir(), key=lambda e: (not e.is_dir(), e.name.lower())):
                    relative_path = os.path.join(parent_path, entry.name).replace("\\", "/")
                    if entry.is_dir():
                        structure.append(relative_path + "/")  # Add trailing slash for directories
                        structure.extend(build_structure(entry, relative_path))
                    else:
                        structure.append(relative_path)
                return structure



            return "\n".join(build_structure(base_dir))
        except FileNotFoundError as fnf_error:
            raise FileNotFoundError(f"Error: {str(fnf_error)}")
        except PermissionError as perm_error:
            raise PermissionError(f"Permission denied: {str(perm_error)}")
        except Exception as e:
            raise RuntimeError(f"An unexpected error occurred: {str(e)}")

    async def generate_screen(self, project_id: str, branch_id: str, request: ScreenRequest) -> Dict[str, Any]:
        """
        Generate a specific screen for the project and branch based on the request
        """
        logger.info(f"Generating screen '{request.screen_name}' for {project_id}/{branch_id}")

        try:
            # Set defaults if not provided
            framework = request.framework or self.DEFAULT_FRAMEWORK
            styling = request.styling or self.DEFAULT_STYLING
            language = request.language or self.DEFAULT_LANGUAGE

            # Get the template prompt
            template = self.template_manager.get_screen_template(request)

            # Generate code
            provider = await self.provider_factory.create_provider("ollama")
            generated_text = await provider.generate_code(
                prompt=template,
                language=language,
                temperature=0.2,
                max_tokens=6000,
                additional_params={
                    "num_ctx": 8192,
                    "top_k": 40,
                    "top_p": 0.9,
                },
            )

            # Extract and save files
            file_sections = self._extract_file_sections(generated_text)
            save_result = await self._save_fe_files(
                project_id, branch_id, file_sections
            )

            return self._build_success_response(
                generation_type="screen",
                framework=framework,
                styling=styling,
                language=language,
                generated_code=generated_text,
                file_sections=file_sections,
                save_result=save_result,
                base_dir=f"projects/{project_id}/branches/{branch_id}/screens"
            )

        except Exception as e:
            logger.error(f"Screen generation failed: {str(e)}", exc_info=True)
            return self._build_error_response("screen", str(e))

    def _extract_file_sections(self, text: str) -> Dict[str, str]:
        """Robust file extraction with multiple pattern matching attempts"""
        file_sections = {}
        
        # Attempt 1: Strict pattern matching
        strict_pattern = r'#\s*File:\s*([^\n]+)\s*```[^\n]*\n([\s\S]*?)\n```'
        matches = re.findall(strict_pattern, text)
        for file_path, content in matches:
            file_sections[file_path.strip()] = content.strip()
        
        if file_sections:
            return file_sections
            
        # Attempt 2: More flexible pattern
        flexible_pattern = r'(?:^|\n)#\s*(?:File|Path):?\s*([^\n]+)\s*(?:```[^\n]*\n([\s\S]*?)\n```|$)'
        matches = re.findall(flexible_pattern, text, re.MULTILINE)
        for file_path, content in matches:
            if content:  # Only add if we got content
                file_sections[file_path.strip()] = content.strip()
        
        if file_sections:
            return file_sections
            
        # Attempt 3: Extract any code blocks
        code_blocks = re.findall(r'```[^\n]*\n([\s\S]*?)\n```', text)
        if code_blocks:
            for i, content in enumerate(code_blocks, 1):
                file_sections[f'src/generated_{i}.tsx'] = content.strip()
        
        return file_sections

    def _sanitize_path(self, path: str) -> Path:
        """Sanitize file paths to prevent directory traversal"""
        path = path.strip().replace("..", "").lstrip("/")
        return Path(path)

    async def _save_fe_files(
        self,
        project_id: str,
        branch_id: str,
        file_sections: Dict[str, str]
    ) -> Dict[str, Any]:
        """Save files with retries and proper error handling"""
        base_dir = Path(f"projects/{project_id}/branches/{branch_id}")
        saved_files = []
        failed_files = []
        
        try:
            base_dir.mkdir(parents=True, exist_ok=True)
            
            for file_path, content in file_sections.items():
                try:
                    clean_path = self._sanitize_path(file_path)
                    if not clean_path.name:  # Skip if no filename
                        continue
                        
                    full_path = base_dir / clean_path
                    full_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Write with retry logic
                    for attempt in range(self.FILE_WRITE_RETRIES):
                        try:
                            with open(full_path, "w", encoding="utf-8") as f:
                                f.write(content)
                            saved_files.append(str(full_path))
                            break
                        except OSError as e:
                            if attempt == self.FILE_WRITE_RETRIES - 1:
                                raise
                            time.sleep(self.FILE_WRITE_RETRY_DELAY)
                except Exception as e:
                    logger.error(f"Failed to save {file_path}: {str(e)}")
                    failed_files.append({
                        "file": file_path,
                        "error": str(e)
                    })
            
            return {
                "saved_files": saved_files,
                "failed_files": failed_files,
                "total_files": len(file_sections)
            }
            
        except Exception as e:
            logger.exception("Critical error during file saving")
            return {
                "saved_files": [],
                "failed_files": [{"error": str(e)}],
                "total_files": 0
            }

    def _build_success_response(
        self,
        generation_type: str,
        framework: str,
        styling: str,
        language: str,
        generated_code: str,
        file_sections: Dict[str, str],
        save_result: Dict[str, Any],
        base_dir: str
    ) -> Dict[str, Any]:
        """Build consistent success response"""
        virtual_files = {
            path: {
                "content": content,
                "size": f"{len(content.encode('utf-8')) / 1024:.1f}KB",
                "checksum": hashlib.md5(content.encode()).hexdigest()
            }
            for path, content in file_sections.items()
        }
        
        return {
            "success": True,
            "generation_type": generation_type,
            "framework": framework,
            "styling": styling,
            "language": language,
            "generated_code": generated_code,
            "file_structure": {
                "virtual_files": virtual_files,
                "save_result": save_result
            },
            "stats": {
                "total_files": len(file_sections),
                "extracted_files": len(file_sections),
                "saved_files": len(save_result.get("saved_files", [])),
                "failed_saves": len(save_result.get("failed_files", []))
            },
            "base_directory": base_dir,
            "message": f"{generation_type.replace('_', ' ').title()} generated successfully"
        }

    def _build_error_response(
        self,
        generation_type: str,
        error: str
    ) -> Dict[str, Any]:
        """Build consistent error response"""
        return {
            "success": False,
            "generation_type": generation_type,
            "error": error,
            "message": f"Failed to generate {generation_type.replace('_', ' ')}: {error}",
            "generated_code": "",
            "file_structure": {
                "virtual_files": {},
                "save_result": {}
            },
            "stats": {
                "total_files": 0,
                "extracted_files": 0,
                "saved_files": 0,
                "failed_saves": 0
            },
            "base_directory": ""
        }
