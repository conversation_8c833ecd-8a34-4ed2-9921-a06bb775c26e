import os
import psutil
import subprocess
import platform
import logging
import requests
from typing import Dict, Any, List, Optional
import asyncio
from fastapi import HTTPException
from app.core.config import get_settings

logger = logging.getLogger(__name__)


class HealthService:
    def __init__(self):
        self.settings = get_settings()

    async def get_system_health(self) -> Dict[str, Any]:
        """Get system health information including CPU, memory, and disk usage."""
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.5)
            cpu_count = psutil.cpu_count(logical=True)
            cpu_physical = psutil.cpu_count(logical=False)

            # Get memory usage
            memory = psutil.virtual_memory()

            # Get disk usage
            disk = psutil.disk_usage("/")

            # Get load average (on Unix systems)
            load_avg = os.getloadavg() if platform.system() != "Windows" else [0, 0, 0]

            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "logical_cores": cpu_count,
                    "physical_cores": cpu_physical,
                    "load_average": {
                        "1min": load_avg[0],
                        "5min": load_avg[1],
                        "15min": load_avg[2],
                    },
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent,
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": disk.percent,
                },
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "processor": platform.processor(),
                },
            }
        except Exception as e:
            logger.error(f"Error checking system health: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to check system health: {str(e)}"
            )

    async def check_ollama_status(self) -> Dict[str, Any]:
        """Check Ollama server status and models."""
        try:
            # Check if Ollama is running
            ollama_url = self.settings.OLLAMA_BASE_URL

            try:
                response = requests.get(f"{ollama_url}")
                ollama_running = response.status_code < 400
            except requests.RequestException:
                ollama_running = False

            # Get list of available models if Ollama is running
            available_models = []
            if ollama_running:
                try:
                    models_response = requests.get(f"{ollama_url}/api/tags")
                    if models_response.status_code == 200:
                        available_models = [
                            model["name"]
                            for model in models_response.json().get("models", [])
                        ]
                except requests.RequestException:
                    pass

            return {
                "ollama_server": {
                    "running": ollama_running,
                    "url": ollama_url,
                    "available_models": available_models,
                }
            }
        except Exception as e:
            logger.error(f"Error checking Ollama status: {str(e)}")
            return {"ollama_server": {"running": False, "error": str(e)}}

    async def start_ollama_server(self) -> Dict[str, Any]:
        """Start Ollama server if not running."""
        try:
            # Check current status
            status = await self.check_ollama_status()

            # If already running, return the status
            if status["ollama_server"]["running"]:
                return {
                    "action": "none",
                    "message": "Ollama server is already running",
                    "ollama_status": status["ollama_server"],
                }

            # Start Ollama server
            if platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", "-a", "Ollama"])
                success = True
                message = "Ollama application started"
            elif platform.system() == "Linux":
                subprocess.Popen(
                    ["ollama", "serve"], stdout=subprocess.PIPE, stderr=subprocess.PIPE
                )
                success = True
                message = "Ollama server started"
            elif platform.system() == "Windows":
                # Windows users typically run Ollama via WSL or a different approach
                subprocess.Popen(["start", "ollama"], shell=True)
                success = True
                message = "Attempted to start Ollama"
            else:
                success = False
                message = f"Unsupported platform: {platform.system()}"

            # Wait a moment for the server to start
            if success:
                await asyncio.sleep(2)
                new_status = await self.check_ollama_status()
                return {
                    "action": "started",
                    "message": message,
                    "ollama_status": new_status["ollama_server"],
                }
            else:
                return {"action": "failed", "message": message}

        except Exception as e:
            logger.error(f"Error starting Ollama server: {str(e)}")
            return {
                "action": "error",
                "message": f"Failed to start Ollama server: {str(e)}",
            }

    async def pull_model(self, model_name: str) -> Dict[str, Any]:
        """Pull a specific Ollama model."""
        try:
            # Check if Ollama is running
            status = await self.check_ollama_status()
            if not status["ollama_server"]["running"]:
                # Try to start Ollama
                start_result = await self.start_ollama_server()
                if not start_result.get("ollama_status", {}).get("running", False):
                    return {
                        "action": "failed",
                        "message": "Cannot pull model because Ollama server could not be started",
                    }

            # Pull the model
            process = subprocess.Popen(
                ["ollama", "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )

            # Add timeout to avoid hanging indefinitely
            try:
                stdout, stderr = process.communicate(timeout=300)  # 5 minutes timeout
                success = process.returncode == 0

                if success:
                    return {
                        "action": "pulled",
                        "model": model_name,
                        "message": "Model pulled successfully",
                    }
                else:
                    return {
                        "action": "failed",
                        "model": model_name,
                        "message": f"Failed to pull model: {stderr}",
                    }
            except subprocess.TimeoutExpired:
                process.kill()
                return {
                    "action": "timeout",
                    "model": model_name,
                    "message": "Timed out while pulling the model",
                }

        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {str(e)}")
            return {
                "action": "error",
                "model": model_name,
                "message": f"Error pulling model: {str(e)}",
            }

    async def get_complete_health_check(self) -> Dict[str, Any]:
        """Perform a complete health check of the system."""
        system_health = await self.get_system_health()
        ollama_status = await self.check_ollama_status()

        # Check database connectivity if applicable
        db_status = {
            "connected": True,  # This should be replaced with actual DB check
            "type": "sqlite",  # Replace with actual DB type from settings
        }

        # Get API uptime
        # This would require tracking when the API started
        # For now, we'll just use system uptime
        uptime = {"system_seconds": psutil.boot_time()}

        # Get application version
        version = self.settings.VERSION

        return {
            "status": "healthy",
            "version": version,
            "uptime": uptime,
            "system": system_health,
            "services": {
                "ollama": ollama_status["ollama_server"],
                "database": db_status,
            },
        }
