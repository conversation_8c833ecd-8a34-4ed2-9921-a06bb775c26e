from typing import Dict, List, Optional, Any, Set
import os
import re
import glob
from pathlib import Path

class FileOperationsService:
    """
    Service for handling file operations related to code generation.
    """
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the file operations service.
        
        Args:
            base_dir: Base directory for file operations (workspace root)
        """
        self.base_dir = base_dir or os.getcwd()
    
    async def read_file(self, file_path: str) -> str:
        """
        Read file content from the specified path.
        
        Args:
            file_path: Path to the file (relative to base_dir)
            
        Returns:
            The file content as a string
            
        Raises:
            FileNotFoundError: If the file does not exist
        """
        full_path = os.path.join(self.base_dir, file_path)
        
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    async def write_file(self, file_path: str, content: str, create_dirs: bool = True) -> None:
        """
        Write content to the specified file path.
        
        Args:
            file_path: Path to the file (relative to base_dir)
            content: Content to write to the file
            create_dirs: Whether to create parent directories if they don't exist
            
        Raises:
            IOError: If the file cannot be written
        """
        full_path = os.path.join(self.base_dir, file_path)
        
        if create_dirs:
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def list_files(self, directory: str = ".", pattern: Optional[str] = None) -> List[str]:
        """
        List files in the specified directory.
        
        Args:
            directory: Directory to list files from (relative to base_dir)
            pattern: Optional glob pattern to filter files
            
        Returns:
            List of file paths (relative to the specified directory)
        """
        full_dir_path = os.path.join(self.base_dir, directory)
        
        if not os.path.exists(full_dir_path):
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        if pattern:
            return glob.glob(os.path.join(full_dir_path, pattern))
        
        # List all files recursively
        files = []
        for root, _, filenames in os.walk(full_dir_path):
            for filename in filenames:
                rel_path = os.path.relpath(os.path.join(root, filename), full_dir_path)
                files.append(rel_path)
        
        return files
    
    async def get_context_files(self, 
                              directory: str = ".", 
                              include_patterns: Optional[List[str]] = None,
                              exclude_patterns: Optional[List[str]] = None,
                              max_files: int = 10,
                              max_file_size_kb: int = 100) -> List[Dict[str, str]]:
        """
        Get a list of files with their content for context.
        
        Args:
            directory: Directory to get files from (relative to base_dir)
            include_patterns: List of glob patterns to include
            exclude_patterns: List of glob patterns to exclude
            max_files: Maximum number of files to include
            max_file_size_kb: Maximum file size in KB
            
        Returns:
            List of dicts with filename and content
        """
        full_dir_path = os.path.join(self.base_dir, directory)
        
        if not os.path.exists(full_dir_path):
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        # Default include all files
        if not include_patterns:
            include_patterns = ["**/*"]
        
        # Default exclude patterns
        if not exclude_patterns:
            exclude_patterns = [
                "**/.git/**",
                "**/node_modules/**",
                "**/__pycache__/**",
                "**/*.pyc",
                "**/*.pyo",
                "**/venv/**",
                "**/.venv/**",
                "**/.env/**",
            ]
        
        # Get all files
        all_files = []
        for pattern in include_patterns:
            pattern_path = os.path.join(full_dir_path, pattern)
            matched_files = [
                os.path.relpath(f, full_dir_path)
                for f in glob.glob(pattern_path, recursive=True)
                if os.path.isfile(f)
            ]
            all_files.extend(matched_files)
        
        # Remove duplicates
        all_files = list(set(all_files))
        
        # Apply exclude patterns
        for pattern in exclude_patterns:
            pattern_path = os.path.join(full_dir_path, pattern)
            exclude_files = set(
                os.path.relpath(f, full_dir_path)
                for f in glob.glob(pattern_path, recursive=True)
                if os.path.isfile(f)
            )
            all_files = [f for f in all_files if f not in exclude_files]
        
        # Sort by modification time (newest first)
        all_files.sort(
            key=lambda f: os.path.getmtime(os.path.join(full_dir_path, f)),
            reverse=True
        )
        
        # Limit to max_files
        all_files = all_files[:max_files]
        
        # Read content
        result = []
        max_size = max_file_size_kb * 1024  # Convert to bytes
        
        for file in all_files:
            file_path = os.path.join(full_dir_path, file)
            
            # Skip files that are too large
            if os.path.getsize(file_path) > max_size:
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                result.append({
                    "filename": file,
                    "content": content
                })
            except UnicodeDecodeError:
                # Skip binary files
                continue
        
        return result
