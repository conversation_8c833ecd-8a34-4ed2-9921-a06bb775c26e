from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>, Any
import re
import os
import tempfile
import subprocess
from abc import ABC, abstractmethod

class CodeValidator(ABC):
    """
    Abstract base class for code validators.
    Different languages will have their own validator implementations.
    """
    
    @abstractmethod
    async def validate(self, code: str, filename: Optional[str] = None) -> Tuple[bool, str]:
        """
        Validate the generated code.
        
        Args:
            code: The code string to validate
            filename: Optional filename (used for extension/language detection)
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        pass


class PythonValidator(CodeValidator):
    """
    Validator for Python code.
    """
    
    async def validate(self, code: str, filename: Optional[str] = None) -> Tuple[bool, str]:
        """
        Validate Python code using Python's built-in compile function.
        
        Args:
            code: The Python code to validate
            filename: Optional filename (ignored for Python validation)
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Try to compile the code to check for syntax errors
            compile(code, '<string>', 'exec')
            return True, "Code is valid."
        except SyntaxError as e:
            return False, f"Syntax error: {str(e)}"
        except Exception as e:
            return False, f"Validation error: {str(e)}"


class JavaScriptValidator(CodeValidator):
    """
    Validator for JavaScript code.
    """
    
    async def validate(self, code: str, filename: Optional[str] = None) -> Tuple[bool, str]:
        """
        Validate JavaScript code using Node.js.
        
        Args:
            code: The JavaScript code to validate
            filename: Optional filename (ignored for JavaScript validation)
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if Node.js is installed
        try:
            subprocess.run(["node", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        except (subprocess.SubprocessError, FileNotFoundError):
            return False, "Node.js is not installed or not in PATH. Cannot validate JavaScript code."
        
        with tempfile.NamedTemporaryFile(suffix=".js", delete=False) as temp:
            temp_name = temp.name
            temp.write(code.encode('utf-8'))
            temp.flush()
        
        try:
            # Try to parse the code with Node.js's parser
            result = subprocess.run(
                ["node", "--check", temp_name],
                check=False,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            os.unlink(temp_name)
            
            if result.returncode == 0:
                return True, "Code is valid."
            else:
                return False, f"JavaScript validation error: {result.stderr}"
        except Exception as e:
            os.unlink(temp_name)
            return False, f"Validation error: {str(e)}"


class ValidationService:
    """
    Service for validating generated code.
    """
    
    def __init__(self):
        """Initialize the validation service with supported validators."""
        self._validators: Dict[str, CodeValidator] = {
            "python": PythonValidator(),
            "javascript": JavaScriptValidator(),
            "js": JavaScriptValidator(),
        }
    
    async def validate_code(self, code: str, language: str) -> Tuple[bool, str]:
        """
        Validate the generated code for a specific language.
        
        Args:
            code: The code string to validate
            language: The programming language
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        language = language.lower()
        
        if language not in self._validators:
            return False, f"No validator available for language: {language}"
        
        validator = self._validators[language]
        return await validator.validate(code)
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages for validation.
        
        Returns:
            List of supported language identifiers
        """
        return list(self._validators.keys())
    
    async def register_validator(self, language: str, validator: CodeValidator) -> None:
        """
        Register a new validator for a language.
        
        Args:
            language: The language identifier (lowercase)
            validator: The validator instance
        """
        self._validators[language.lower()] = validator
