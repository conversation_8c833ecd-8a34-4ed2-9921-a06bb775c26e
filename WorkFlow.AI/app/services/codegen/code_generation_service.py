from typing import Dict, List, Optional, Any
import os
from app.services.codegen.provider_factory import ProviderFactory
from app.services.codegen.llm_provider_base import LLMProviderBase

class CodeGenerationService:
    """
    Service for generating code using various LLM providers.
    """
    
    def __init__(self, default_provider: str = "openai"):
        """
        Initialize the code generation service.
        
        Args:
            default_provider: The default provider to use if none specified
        """
        self.default_provider = default_provider
        self._provider_instances: Dict[str, LL<PERSON>roviderBase] = {}
        
    async def _get_provider(self, provider_name: Optional[str] = None, **provider_kwargs) -> LLMProviderBase:
        """
        Get or create a provider instance.
        
        Args:
            provider_name: Name of the provider to use (or default if None)
            **provider_kwargs: Additional arguments to pass to the provider constructor
            
        Returns:
            A provider instance
        """
        provider_name = provider_name or self.default_provider
        
        # Check if we have a cached instance with the same config
        if provider_name in self._provider_instances:
            return self._provider_instances[provider_name]
        
        # Create a new provider instance
        provider = await ProviderFactory.create_provider(provider_name, **provider_kwargs)
        self._provider_instances[provider_name] = provider
        return provider
    
    async def generate_code(self,
                          prompt: str,
                          language: str,
                          provider_name: Optional[str] = None,
                          model: Optional[str] = None,
                          temperature: float = 0.7,
                          max_tokens: int = 2048,
                          additional_params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate code using a specified provider.
        
        Args:
            prompt: The prompt describing what code to generate
            language: The programming language for the generated code
            provider_name: Name of the provider to use (or default if None)
            model: Model identifier to use (provider-specific)
            temperature: Controls randomness (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            additional_params: Additional provider-specific parameters
            
        Returns:
            The generated code as a string
        """
        provider_kwargs = {}
        if model:
            provider_kwargs['model'] = model
            
        provider = await self._get_provider(provider_name, **provider_kwargs)
        
        return await provider.generate_code(
            prompt=prompt,
            language=language,
            temperature=temperature,
            max_tokens=max_tokens,
            additional_params=additional_params
        )
    
    async def generate_code_with_context(self,
                                      prompt: str,
                                      context_files: List[Dict[str, str]],
                                      language: str,
                                      provider_name: Optional[str] = None,
                                      model: Optional[str] = None,
                                      temperature: float = 0.7,
                                      max_tokens: int = 2048,
                                      additional_params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate code with context files using a specified provider.
        
        Args:
            prompt: The prompt describing what code to generate
            context_files: List of files with their content to provide as context
                          [{"filename": "example.py", "content": "def example()..."}]
            language: The programming language for the generated code
            provider_name: Name of the provider to use (or default if None)
            model: Model identifier to use (provider-specific)
            temperature: Controls randomness (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            additional_params: Additional provider-specific parameters
            
        Returns:
            The generated code as a string
        """
        provider_kwargs = {}
        if model:
            provider_kwargs['model'] = model
            
        provider = await self._get_provider(provider_name, **provider_kwargs)
        
        return await provider.generate_code_with_context(
            prompt=prompt,
            context_files=context_files,
            language=language,
            temperature=temperature,
            max_tokens=max_tokens,
            additional_params=additional_params
        )
    
    async def get_available_providers(self) -> List[str]:
        """
        Get a list of available provider names.
        
        Returns:
            List of provider names
        """
        return ProviderFactory.get_available_providers()
    
    async def get_supported_models(self, provider_name: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Get supported models for a specific provider or all providers.
        
        Args:
            provider_name: Name of the provider (or None for all providers)
            
        Returns:
            Dict mapping provider names to lists of supported models
        """
        if provider_name:
            return {provider_name: await ProviderFactory.get_supported_models(provider_name)}
        
        result = {}
        for provider in await self.get_available_providers():
            result[provider] = await ProviderFactory.get_supported_models(provider)
        
        return result 