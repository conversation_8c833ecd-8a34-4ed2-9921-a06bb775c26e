from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

class LLMProviderBase(ABC):
    """
    Abstract base class for all LLM providers.
    Each provider implementation must inherit from this class.
    """
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the LLM provider with necessary configurations"""
        pass
    
    @abstractmethod
    async def generate_code(self, 
                            prompt: str, 
                            language: str, 
                            temperature: float = 0.7, 
                            max_tokens: int = 2048,
                            additional_params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate code using the LLM provider
        
        Args:
            prompt: The prompt describing what code to generate
            language: The programming language for the generated code
            temperature: Controls randomness (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            additional_params: Additional provider-specific parameters
            
        Returns:
            The generated code as a string
        """
        pass
    
    @abstractmethod
    async def generate_code_with_context(self,
                                        prompt: str,
                                        context_files: List[Dict[str, str]],
                                        language: str,
                                        temperature: float = 0.7,
                                        max_tokens: int = 2048,
                                        additional_params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate code with additional context files
        
        Args:
            prompt: The prompt describing what code to generate
            context_files: List of files with their content to provide as context
                          [{"filename": "example.py", "content": "def example()..."}]
            language: The programming language for the generated code
            temperature: Controls randomness (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            additional_params: Additional provider-specific parameters
            
        Returns:
            The generated code as a string
        """
        pass
    
    @abstractmethod
    async def get_supported_models(self) -> List[str]:
        """Return a list of model identifiers supported by this provider"""
        pass 