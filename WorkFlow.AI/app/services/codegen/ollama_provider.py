from typing import Dict, Any, Optional, List
import os
from langchain_ollama import OllamaLLM
from langchain.schema import HumanMessage, SystemMessage
from app.services.codegen.llm_provider_base import LLMProviderBase


class OllamaProvider(LLMProviderBase):
    """
    Implementation of LLMProviderBase for Ollama models.
    """

    def __init__(self, base_url: Optional[str] = None, model: str = "deepseek-coder:33b"):
        """
        Initialize the Ollama provider.

        Args:
            base_url: URL to Ollama API (if None, uses default or from env)
            model: The model to use (default: codellama:latest)
        """
        self.base_url = base_url or os.getenv(
            "OLLAMA_BASE_URL", "http://localhost:11434"
        )
        self.model = model
        self.client = None

    async def initialize(self) -> None:
        """Initialize the Ollama client"""
        self.client = OllamaLLM(
            base_url=self.base_url,
            model=self.model,
            temperature=0,  # Default temp, will be overridden in generate methods
        )

    async def generate_code(
        self,
        prompt: str,
        language: str,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        additional_params: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Generate code using Ollama"""
        if not self.client:
            await self.initialize()

        # Configure client with request-specific parameters
        self.client.temperature = temperature

        # Note: Ollama doesn't directly support max_tokens parameter in the same way
        # If additional_params includes num_predict or num_tokens, we can use that
        if additional_params:
            for key, value in additional_params.items():
                if hasattr(self.client, key):
                    setattr(self.client, key, value)

        # Add this after setting temperature
        if max_tokens and hasattr(self.client, "num_predict"):
            self.client.num_predict = max_tokens

        # Create system prompt and user prompt
        system_prompt = f"You are an expert programmer. Generate {language} code that accomplishes the following task. Provide only the code, no explanations or comments unless specifically requested."

        full_prompt = f"{system_prompt}\n\n{prompt}"

        # Generate the response
        response = await self.client.ainvoke(full_prompt)
        return response

    async def generate_code_with_context(
        self,
        prompt: str,
        context_files: List[Dict[str, str]],
        language: str,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        additional_params: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Generate code with context using Ollama"""
        if not self.client:
            await self.initialize()

        # Configure client with request-specific parameters
        self.client.temperature = temperature

        # Note: Ollama doesn't directly support max_tokens parameter in the same way
        # If additional_params includes num_predict or num_tokens, we can use that
        if additional_params:
            for key, value in additional_params.items():
                if hasattr(self.client, key):
                    setattr(self.client, key, value)

        # Add this after setting temperature
        if max_tokens and hasattr(self.client, "num_predict"):
            self.client.num_predict = max_tokens

        # Format context files
        context_str = "\n\n".join(
            [
                f"File: {file['filename']}\n```\n{file['content']}\n```"
                for file in context_files
            ]
        )

        system_prompt = f"You are an expert programmer. Generate {language} code that accomplishes the following task. Consider the provided context files. Provide only the code, no explanations or comments unless specifically requested."

        full_prompt = f"""
{system_prompt}

I need you to write {language} code based on the following task:

{prompt}

Here are some context files to help you understand the project structure and style:

{context_str}

Generate only the code without explanations.
"""

        # Generate the response
        response = await self.client.ainvoke(full_prompt)
        return response

    async def get_supported_models(self) -> List[str]:
        """Return the supported Ollama models for code generation"""
        if not self.client:
            await self.initialize()
            
        try:
            # Query Ollama API for available models
            response = await self.client.alist_models()
            
            # Extract model names from the response
            # The response structure typically contains a list of model objects
            # with a 'name' field for each model
            available_models = [model['name'] for model in response['models']] if 'models' in response else []
            
            return available_models
        except Exception as e:
            # If there's an error fetching models, log it and return an empty list
            print(f"Error fetching Ollama models: {str(e)}")
            return []
