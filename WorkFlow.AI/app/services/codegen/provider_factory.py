from typing import Dict, List, Optional, Type, Any
from app.services.codegen.llm_provider_base import LLMProviderBase
from app.services.codegen.openai_provider import OpenAIProvider
from app.services.codegen.groq_provider import GroqProvider
from app.services.codegen.ollama_provider import OllamaProvider

class ProviderFactory:
    """
    Factory class to create and manage LLM providers.
    """
    
    # Map provider names to their class implementations
    _providers: Dict[str, Type[LLMProviderBase]] = {
        "openai": OpenAIProvider,
        "groq": GroqProvider,
        "ollama": OllamaProvider
    }
    
    @classmethod
    async def create_provider(cls, 
                            provider_name: str, 
                            **kwargs) -> LLMProviderBase:
        """
        Create and initialize a provider instance.
        
        Args:
            provider_name: Name of the provider (openai, groq, ollama)
            **kwargs: Additional arguments to pass to the provider constructor
            
        Returns:
            An initialized provider instance
            
        Raises:
            ValueError: If provider_name is not supported
        """
        provider_class = cls._providers.get(provider_name.lower())
        
        if not provider_class:
            raise ValueError(f"Unsupported provider: {provider_name}. Available providers: {list(cls._providers.keys())}")
        
        provider = provider_class(**kwargs)
        await provider.initialize()
        return provider
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """
        Get list of available provider names.
        
        Returns:
            List of provider names
        """
        return list(cls._providers.keys())
    
    @classmethod
    async def get_supported_models(cls, provider_name: str) -> List[str]:
        """
        Get list of supported models for a specific provider.
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            List of model identifiers supported by the provider
            
        Raises:
            ValueError: If provider_name is not supported
        """
        provider = await cls.create_provider(provider_name)
        return await provider.get_supported_models() 