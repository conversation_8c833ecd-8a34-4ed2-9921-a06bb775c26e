from typing import Dict, Any, Optional, List
import os
from langchain_groq import ChatGroq
from langchain.schema import HumanMessage, SystemMessage
from app.services.codegen.llm_provider_base import LLMProviderBase

class GroqProvider(LLMProviderBase):
    """
    Implementation of LLMProviderBase for Groq's models.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "llama3-70b-8192"):
        """
        Initialize the Groq provider.
        
        Args:
            api_key: Groq API key (if None, fetches from environment)
            model: The model to use (default: llama3-70b-8192)
        """
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.model = model
        self.client = None
        
        if not self.api_key:
            raise ValueError("Groq API key is required")
        
    async def initialize(self) -> None:
        """Initialize the Groq client"""
        self.client = ChatGroq(
            model=self.model,
            api_key=self.api_key,
            temperature=0,  # Default temp, will be overridden in generate methods
        )
    
    async def generate_code(self, 
                          prompt: str, 
                          language: str,
                          temperature: float = 0.7, 
                          max_tokens: int = 2048,
                          additional_params: Optional[Dict[str, Any]] = None) -> str:
        """Generate code using Groq"""
        if not self.client:
            await self.initialize()
            
        # Configure client with request-specific parameters
        self.client.temperature = temperature
        self.client.max_tokens = max_tokens
        
        if additional_params:
            for key, value in additional_params.items():
                if hasattr(self.client, key):
                    setattr(self.client, key, value)
        
        # Create messages for the chat
        system_prompt = f"You are an expert programmer. Generate {language} code that accomplishes the following task. Provide only the code, no explanations or comments unless specifically requested."
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=prompt)
        ]
        
        # Generate the response
        response = await self.client.ainvoke(messages)
        return response.content
    
    async def generate_code_with_context(self,
                                       prompt: str,
                                       context_files: List[Dict[str, str]],
                                       language: str,
                                       temperature: float = 0.7,
                                       max_tokens: int = 2048,
                                       additional_params: Optional[Dict[str, Any]] = None) -> str:
        """Generate code with context using Groq"""
        if not self.client:
            await self.initialize()
            
        # Configure client with request-specific parameters
        self.client.temperature = temperature
        self.client.max_tokens = max_tokens
        
        if additional_params:
            for key, value in additional_params.items():
                if hasattr(self.client, key):
                    setattr(self.client, key, value)
        
        # Format context files
        context_str = "\n\n".join([
            f"File: {file['filename']}\n```\n{file['content']}\n```"
            for file in context_files
        ])
        
        # Create messages for the chat
        system_prompt = f"You are an expert programmer. Generate {language} code that accomplishes the following task. Consider the provided context files. Provide only the code, no explanations or comments unless specifically requested."
        
        user_prompt = f"""
I need you to write {language} code based on the following task:

{prompt}

Here are some context files to help you understand the project structure and style:

{context_str}

Generate only the code without explanations.
"""
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        # Generate the response
        response = await self.client.ainvoke(messages)
        return response.content
    
    async def get_supported_models(self) -> List[str]:
        """Return the supported Groq models for code generation"""
        return [
            "llama3-70b-8192",
            "llama3-8b-8192",
            "mixtral-8x7b-32768",
            "gemma-7b-it"
        ] 