from typing import Dict, Any
import json
import httpx
from app.models.pydantic_models.crud_gen import (
    AddApiRequest,
    SearchApiRequest,
    ListApiRequest,
    DeleteApiRequest,
    FetchApiRequest,
)
from app.services.codegen.provider_factory import ProviderFactory
from app.utils.logger import get_logger
from app.utils.helpers import (
    extract_language_from_prompt,
    extract_database_from_prompt,
    extract_file_sections,
    save_generated_files,
)
from app.templates.template import (
    get_database_template,
    get_list_database_template,
    get_search_database_template,
    get_delete_template,
    get_fetch_template,
    SEARCH_TEMPLATE_SPECIFIC,
    LIST_TEMPLATE_SPECIFIC,
)

from app.templates.prompts import (
  BASE_PROMPT,
  UPDATE_PROMPT,
  OVERWRITE_PROMPT,
  SEARCH_BASE_PROMPT,
  LIST_BASE_PROMPT,
  FETCH_BASE_PROMPT,
  DELETE_BASE_PROMPT,
)

# Get a logger instance for this module
logger = get_logger(__name__)


class CrudGenService:
    """Service layer for handling API code operations within a project branch."""

    async def add_api(
        self, project_id: str, branch_id: str, request: AddApiRequest
    ) -> dict:
        """
        Generates API code to create resource from user prompt using Ollama.

        Args:
            project_id: The project identifier
            branch_id: The branch identifier
            request: Request containing the prompt

        Returns:
            Dictionary with generated code and file information
        """
        logger.info(
            f"Starting API generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt: {prompt}")

        # Extract language from prompt
        language, processed_prompt = extract_language_from_prompt(prompt)
        logger.info(f"Detected language: {language}")

        # Extract database type from prompt
        database_type, processed_prompt = extract_database_from_prompt(processed_prompt)
        logger.info(f"Detected database: {database_type}")

        # Get the database-specific template
        template = get_database_template(language, database_type)
        logger.debug(f"Selected template key: {language}_{database_type}")

        formatted_prompt = template.format(
            prompt=processed_prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the API code using Ollama with increased context window
            logger.info("Generating code with Ollama")
            try:
                generated_text = await provider.generate_code(
                    prompt=formatted_prompt,
                    language=language,  # Pass the extracted language
                    temperature=0.3,
                    max_tokens=8000,
                    additional_params={"num_ctx": 8192},  # Increased context size
                )
                logger.debug(f"Generated text length: {len(generated_text)} characters")
                logger.debug(
                    f"First 500 chars of generated text: {generated_text[:500]}..."
                )
            except httpx.ConnectError as e:
                logger.error(f"Connection error to Ollama: {str(e)}")
                return {
                    "success": False,
                    "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                    "error": "Ollama service is not available. Please start the Ollama service and try again.",
                    "error_type": "connection_error",
                    "details": str(e),
                }
            except TimeoutError:
                logger.error("Connection to Ollama timed out")
                return {
                    "success": False,
                    "message": "Connection to Ollama timed out. Please check if Ollama is running correctly.",
                    "error": "Connection timeout",
                    "error_type": "timeout_error",
                }

            # Extract file paths and code content
            logger.info("Extracting file sections from generated text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections")
            for file_path in file_sections.keys():
                logger.debug(
                    f"Extracted file: {file_path} ({len(file_sections[file_path])} chars)"
                )

            # Save all files to the filesystem
            logger.info("Saving generated files to filesystem")
            file_info = await save_generated_files(project_id, branch_id, file_sections)
            logger.debug(f"File save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"API code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except httpx.ConnectError as e:
            error_message = "Connection error to Ollama service"
            logger.error(f"{error_message}: {str(e)}")
            return {
                "success": False,
                "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                "error": "Ollama service is not available. Please start the Ollama service and try again.",
                "error_type": "connection_error",
                "details": str(e),
            }
        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error generating API code: {str(e)}", exc_info=True)

            # Handle any generation or processing errors
            error_type = "unknown_error"
            if "connection" in str(e).lower() or "connect" in str(e).lower():
                error_type = "connection_error"
                error_message = "Could not connect to the Ollama service. Please ensure Ollama is running."
            else:
                error_message = f"Failed to generate API code: {str(e)}"

            return {
                "success": False,
                "message": error_message,
                "error": str(e),
                "error_type": error_type,
            }


    async def update_api(
        self, project_id: str, branch_id: str, request: AddApiRequest
    ) -> dict:
        """
        Generates API code focused on UPDATE operations based on user prompt using Ollama.

        Args:
            project_id: The project identifier.
            branch_id: The branch identifier.
            request: Request containing the prompt.

        Returns:
            Dictionary with generated code and file information.
        """
        logger.info(
            f"Starting API UPDATE generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt for update: {prompt}")

        # Extract language and database from the original prompt
        language, _ = extract_language_from_prompt(prompt)
        logger.info(f"Detected language for update: {language}")
        database_type, _ = extract_database_from_prompt(prompt)
        logger.info(f"Detected database for update: {database_type}")

        # Format the specific UPDATE prompt
        formatted_prompt = UPDATE_PROMPT.format(
            prompt=prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted UPDATE prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider for update")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the API code using Ollama
            logger.info("Generating update code with Ollama")
            try:
                generated_text = await provider.generate_code(
                    prompt=formatted_prompt,
                    language=language,
                    temperature=0.3,
                    max_tokens=8000,
                    additional_params={"num_ctx": 8192},
                )
                logger.debug(f"Generated update text length: {len(generated_text)} characters")
                logger.debug(
                    f"First 500 chars of generated update text: {generated_text[:500]}..."
                )
            except httpx.ConnectError as e:
                logger.error(f"Connection error to Ollama during update: {str(e)}")
                return {
                    "success": False,
                    "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                    "error": "Ollama service is not available.",
                    "error_type": "connection_error",
                    "details": str(e),
                }
            except TimeoutError:
                logger.error("Connection to Ollama timed out during update")
                return {
                    "success": False,
                    "message": "Connection to Ollama timed out.",
                    "error": "Connection timeout",
                    "error_type": "timeout_error",
                }

            # Extract file paths and code content
            logger.info("Extracting file sections from generated update text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections for update")

            # Save all files to the filesystem
            logger.info("Saving generated update files to filesystem")
            file_info = await save_generated_files(project_id, branch_id, file_sections)
            logger.debug(f"Update file save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"API update code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except httpx.ConnectError as e:
            error_message = "Connection error to Ollama service during update"
            logger.error(f"{error_message}: {str(e)}")
            return {
                "success": False,
                "message": "Could not connect to Ollama service.",
                "error": "Ollama service is not available.",
                "error_type": "connection_error",
                "details": str(e),
            }
        except Exception as e:
            logger.error(f"Error generating API update code: {str(e)}", exc_info=True)
            error_type = "unknown_error"
            if "connection" in str(e).lower() or "connect" in str(e).lower():
                error_type = "connection_error"
                error_message = "Could not connect to the Ollama service."
            else:
                error_message = f"Failed to generate API update code: {str(e)}"
            return {
                "success": False,
                "message": error_message,
                "error": str(e),
                "error_type": error_type,
            }


    async def overwrite_api(
        self, project_id: str, branch_id: str, request: AddApiRequest
    ) -> dict:
        """
        Generates a complete CRUD API application based on user prompt using Ollama.

        Args:
            project_id: The project identifier.
            branch_id: The branch identifier.
            request: Request containing the prompt.

        Returns:
            Dictionary with generated code and file information.
        """
        logger.info(
            f"Starting API OVERWRITE (full CRUD) generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt for overwrite: {prompt}")

        # Extract language and database from the original prompt
        language, _ = extract_language_from_prompt(prompt)
        logger.info(f"Detected language for overwrite: {language}")
        database_type, _ = extract_database_from_prompt(prompt)
        logger.info(f"Detected database for overwrite: {database_type}")

        # Format the specific OVERWRITE prompt
        formatted_prompt = OVERWRITE_PROMPT.format(
            prompt=prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted OVERWRITE prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider for overwrite")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the API code using Ollama
            logger.info("Generating overwrite code with Ollama")
            try:
                generated_text = await provider.generate_code(
                    prompt=formatted_prompt,
                    language=language,
                    temperature=0.3,
                    max_tokens=8000, # Consider if overwrite needs more tokens
                    additional_params={"num_ctx": 8192},
                )
                logger.debug(f"Generated overwrite text length: {len(generated_text)} characters")
                logger.debug(
                    f"First 500 chars of generated overwrite text: {generated_text[:500]}..."
                )
            except httpx.ConnectError as e:
                logger.error(f"Connection error to Ollama during overwrite: {str(e)}")
                return {
                    "success": False,
                    "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                    "error": "Ollama service is not available.",
                    "error_type": "connection_error",
                    "details": str(e),
                }
            except TimeoutError:
                logger.error("Connection to Ollama timed out during overwrite")
                return {
                    "success": False,
                    "message": "Connection to Ollama timed out.",
                    "error": "Connection timeout",
                    "error_type": "timeout_error",
                }

            # Extract file paths and code content
            logger.info("Extracting file sections from generated overwrite text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections for overwrite")

            # Save all files to the filesystem
            logger.info("Saving generated overwrite files to filesystem")
            file_info = await save_generated_files(project_id, branch_id, file_sections)
            logger.debug(f"Overwrite file save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"API overwrite code (full CRUD) generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except httpx.ConnectError as e:
            error_message = "Connection error to Ollama service during overwrite"
            logger.error(f"{error_message}: {str(e)}")
            return {
                "success": False,
                "message": "Could not connect to Ollama service.",
                "error": "Ollama service is not available.",
                "error_type": "connection_error",
                "details": str(e),
            }
        except Exception as e:
            logger.error(f"Error generating API overwrite code: {str(e)}", exc_info=True)
            error_type = "unknown_error"
            if "connection" in str(e).lower() or "connect" in str(e).lower():
                error_type = "connection_error"
                error_message = "Could not connect to the Ollama service."
            else:
                error_message = f"Failed to generate API overwrite code: {str(e)}"
            return {
                "success": False,
                "message": error_message,
                "error": str(e),
                "error_type": error_type,
            }

    async def delete_api(
        self, project_id: str, branch_id: str, request: DeleteApiRequest
    ) -> dict:
        """
        Generates delete API code from a natural language prompt using Ollama.

        Args:
            project_id: The project identifier
            branch_id: The branch identifier
            request: Request containing the prompt for delete API generation

        Returns:
            Dictionary with generated code and file information
        """
        logger.info(
            f"Starting delete API generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt: {prompt}")

        # Extract language from prompt
        language, processed_prompt = extract_language_from_prompt(prompt)
        logger.info(f"Detected language: {language}")

        # Extract database type from prompt
        database_type, processed_prompt = extract_database_from_prompt(processed_prompt)
        logger.info(f"Detected database: {database_type}")

        # Use the DELETE_BASE_PROMPT directly instead of the database-specific template
        # This ensures we only generate delete functionality, not full CRUD
        logger.debug(f"Using DELETE_BASE_PROMPT for {language}_{database_type}")

        formatted_prompt = DELETE_BASE_PROMPT.format(
            prompt=processed_prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the delete API code using Ollama with increased context window
            logger.info("Generating delete API code with Ollama")
            generated_text = await provider.generate_code(
                prompt=formatted_prompt,
                language=language,  # Pass the extracted language
                temperature=0.3,
                max_tokens=8000,
                additional_params={"num_ctx": 8192},  # Increased context size
            )
            logger.debug(f"Generated text length: {len(generated_text)} characters")
            logger.debug(
                f"First 500 chars of generated text: {generated_text[:500]}..."
            )

            # Extract file paths and code content
            logger.info("Extracting file sections from generated text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections")
            for file_path in file_sections.keys():
                logger.debug(
                    f"Extracted file: {file_path} ({len(file_sections[file_path])} chars)"
                )

            # Save all files to the filesystem
            logger.info("Saving generated files to filesystem")
            file_info = await save_generated_files(
                project_id, branch_id, file_sections
            )
            logger.debug(f"File save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"Delete API code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error generating delete API code: {str(e)}", exc_info=True)

            # Handle any generation or processing errors
            return {
                "success": False,
                "message": f"Failed to generate delete API code: {str(e)}",
                "error": str(e),
            }

    async def fetch_api(
        self, project_id: str, branch_id: str, request: FetchApiRequest
    ) -> dict:
        """
        Generates fetch/get API code from a natural language prompt using Ollama.

        Args:
            project_id: The project identifier
            branch_id: The branch identifier
            request: Request containing the prompt for fetch API generation

        Returns:
            Dictionary with generated code and file information
        """
        logger.info(
            f"Starting fetch API generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt: {prompt}")

        # Extract language from prompt
        language, processed_prompt = extract_language_from_prompt(prompt)
        logger.info(f"Detected language: {language}")

        # Extract database type from prompt
        database_type, processed_prompt = extract_database_from_prompt(processed_prompt)
        logger.info(f"Detected database: {database_type}")

        # Use the FETCH_BASE_PROMPT directly instead of the database-specific template
        # This ensures we only generate fetch functionality, not full CRUD
        logger.debug(f"Using FETCH_BASE_PROMPT for {language}_{database_type}")

        formatted_prompt = FETCH_BASE_PROMPT.format(
            prompt=processed_prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the fetch API code using Ollama with increased context window
            logger.info("Generating fetch API code with Ollama")
            generated_text = await provider.generate_code(
                prompt=formatted_prompt,
                language=language,  # Pass the extracted language
                temperature=0.3,
                max_tokens=8000,
                additional_params={"num_ctx": 8192},  # Increased context size
            )
            logger.debug(f"Generated text length: {len(generated_text)} characters")
            logger.debug(
                f"First 500 chars of generated text: {generated_text[:500]}..."
            )

            # Extract file paths and code content
            logger.info("Extracting file sections from generated text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections")
            for file_path in file_sections.keys():
                logger.debug(
                    f"Extracted file: {file_path} ({len(file_sections[file_path])} chars)"
                )

            # Save all files to the filesystem
            logger.info("Saving generated files to filesystem")
            file_info = await save_generated_files(
                project_id, branch_id, file_sections
            )
            logger.debug(f"File save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"Fetch API code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error generating fetch API code: {str(e)}", exc_info=True)

            # Handle any generation or processing errors
            return {
                "success": False,
                "message": f"Failed to generate fetch API code: {str(e)}",
                "error": str(e),
            }


    async def list_api(
        self, project_id: str, branch_id: str, request: ListApiRequest
    ) -> dict:
        """
        Generates list API code based on user prompt using Ollama and LIST_BASE_PROMPT.

        Args:
            project_id: The project identifier
            branch_id: The branch identifier
            request: Request containing the prompt for the list API

        Returns:
            Dictionary with generated code and file information
        """
        logger.info(
            f"Starting List API generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt: {prompt}")

        # Extract language from prompt
        language, processed_prompt = extract_language_from_prompt(prompt)
        logger.info(f"Detected language: {language}")

        # Extract database type from prompt
        database_type, processed_prompt = extract_database_from_prompt(processed_prompt)
        logger.info(f"Detected database: {database_type}")

        # Use the LIST_BASE_PROMPT directly instead of the database-specific template
        # This ensures we only generate list functionality, not full CRUD
        logger.debug(f"Using LIST_BASE_PROMPT for {language}_{database_type}")

        formatted_prompt = LIST_BASE_PROMPT.format(
            prompt=processed_prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the list API code using Ollama
            logger.info("Generating code with Ollama")
            try:
                # Keep max_tokens and num_ctx same as search_api for now
                generated_text = await provider.generate_code(
                    prompt=formatted_prompt,
                    language=language,
                    temperature=0.3,
                    max_tokens=8000,
                    additional_params={"num_ctx": 8000},
                )
                logger.debug(f"Generated text length: {len(generated_text)} characters")
                logger.debug(
                    f"First 500 chars of generated text: {generated_text[:500]}..."
                )
            except httpx.ConnectError as e:
                logger.error(f"Connection error to Ollama: {str(e)}")
                return {
                    "success": False,
                    "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                    "error": "Ollama service is not available. Please start the Ollama service and try again.",
                    "error_type": "connection_error",
                    "details": str(e),
                }
            except TimeoutError:
                logger.error("Connection to Ollama timed out")
                return {
                    "success": False,
                    "message": "Connection to Ollama timed out. Please check if Ollama is running correctly.",
                    "error": "Connection timeout",
                    "error_type": "timeout_error",
                }

            # Extract file paths and code content
            logger.info("Extracting file sections from generated text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections")
            for file_path in file_sections.keys():
                logger.debug(
                    f"Extracted file: {file_path} ({len(file_sections[file_path])} chars)"
                )

            # Save all files to the filesystem
            logger.info("Saving generated files to filesystem")
            file_info = await save_generated_files(project_id, branch_id, file_sections)
            logger.debug(f"File save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"List API code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except httpx.ConnectError as e:
            error_message = "Connection error to Ollama service"
            logger.error(f"{error_message}: {str(e)}")
            return {
                "success": False,
                "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                "error": "Ollama service is not available. Please start the Ollama service and try again.",
                "error_type": "connection_error",
                "details": str(e),
            }
        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error generating API code: {str(e)}", exc_info=True)

            # Handle any generation or processing errors
            error_type = "unknown_error"
            if "connection" in str(e).lower() or "connect" in str(e).lower():
                error_type = "connection_error"
                error_message = "Could not connect to the Ollama service. Please ensure Ollama is running."
            else:
                error_message = f"Failed to generate List API code: {str(e)}" # Specific message

            return {
                "success": False,
                "message": error_message,
                "error": str(e),
                "error_type": error_type,
            }

    async def search_api(
        self, project_id: str, branch_id: str, request: SearchApiRequest
    ) -> dict:
        """
        Generates search API code based on user prompt using Ollama and SEARCH_BASE_PROMPT.

        Args:
            project_id: The project identifier
            branch_id: The branch identifier
            request: Request containing the prompt for the search API

        Returns:
            Dictionary with generated code and file information
        """
        logger.info(
            f"Starting Search API generation for project {project_id}, branch {branch_id}"
        )

        # Get the prompt from the request
        prompt = request.prompt
        logger.debug(f"Original prompt: {prompt}")

        # Extract language from prompt
        language, processed_prompt = extract_language_from_prompt(prompt)
        logger.info(f"Detected language: {language}")

        # Extract database type from prompt
        database_type, processed_prompt = extract_database_from_prompt(processed_prompt)
        logger.info(f"Detected database: {database_type}")

        # Use the SEARCH_BASE_PROMPT directly instead of the database-specific template
        # This ensures we only generate search functionality, not full CRUD
        logger.debug(f"Using SEARCH_BASE_PROMPT for {language}_{database_type}")

        formatted_prompt = SEARCH_BASE_PROMPT.format(
            prompt=processed_prompt, language=language, database=database_type
        )
        logger.debug(f"Formatted prompt: {formatted_prompt[:500]}... (truncated)")

        try:
            # Get the Ollama provider
            logger.info("Creating Ollama provider")
            provider = await ProviderFactory.create_provider("ollama")

            # Generate the search API code using Ollama
            logger.info("Generating code with Ollama")
            try:
                generated_text = await provider.generate_code(
                    prompt=formatted_prompt,
                    language=language,
                    temperature=0.3,
                    max_tokens=8000,
                    additional_params={"num_ctx": 8000},
                )
                logger.debug(f"Generated text length: {len(generated_text)} characters")
                logger.debug(
                    f"First 500 chars of generated text: {generated_text[:500]}..."
                )
            except httpx.ConnectError as e:
                logger.error(f"Connection error to Ollama: {str(e)}")
                return {
                    "success": False,
                    "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                    "error": "Ollama service is not available. Please start the Ollama service and try again.",
                    "error_type": "connection_error",
                    "details": str(e),
                }
            except TimeoutError:
                logger.error("Connection to Ollama timed out")
                return {
                    "success": False,
                    "message": "Connection to Ollama timed out. Please check if Ollama is running correctly.",
                    "error": "Connection timeout",
                    "error_type": "timeout_error",
                }

            # Extract file paths and code content
            logger.info("Extracting file sections from generated text")
            file_sections = extract_file_sections(generated_text)
            logger.info(f"Extracted {len(file_sections)} file sections")
            for file_path in file_sections.keys():
                logger.debug(
                    f"Extracted file: {file_path} ({len(file_sections[file_path])} chars)"
                )

            # Save all files to the filesystem
            logger.info("Saving generated files to filesystem")
            file_info = await save_generated_files(project_id, branch_id, file_sections)
            logger.debug(f"File save results: {json.dumps(file_info, indent=2)}")

            # Return a simplified response
            return {
                "success": True,
                "message": f"Search API code generated in {language} with {database_type} database and saved successfully.",
                "language": language,
                "database": database_type,
                "generated_code": generated_text,
                "file_info": file_info,
            }

        except httpx.ConnectError as e:
            error_message = "Connection error to Ollama service"
            logger.error(f"{error_message}: {str(e)}")
            return {
                "success": False,
                "message": "Could not connect to Ollama service. Please ensure Ollama is running.",
                "error": "Ollama service is not available. Please start the Ollama service and try again.",
                "error_type": "connection_error",
                "details": str(e),
            }
        except Exception as e:
            # Log the error with traceback
            logger.error(f"Error generating API code: {str(e)}", exc_info=True)

            # Handle any generation or processing errors
            error_type = "unknown_error"
            if "connection" in str(e).lower() or "connect" in str(e).lower():
                error_type = "connection_error"
                error_message = "Could not connect to the Ollama service. Please ensure Ollama is running."
            else:
                error_message = f"Failed to generate Search API code: {str(e)}" # Specific message

            return {
                "success": False,
                "message": error_message,
                "error": str(e),
                "error_type": error_type,
            }