import os
from typing import List, Union, Dict, Any, Optional
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""

    # API configuration
    API_V1_STR: str = ""
    PROJECT_NAME: str = "WorkFlow.AI"
    PROJECT_DESCRIPTION: str = "AI-powered workflow automation and code generation"
    VERSION: str = "0.1.0"

    # CORS configuration
    BACKEND_CORS_ORIGINS: List[Union[str, AnyHttpUrl]] = [
        "http://localhost:3000",
        "http://localhost:8000",
    ]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database configuration
    DATABASE_URL: Optional[str] = "sqlite:///./app.db"

    # GitLab configuration
    GITLAB_URL: str = "https://gitlab.com"
    GITLAB_TOKEN: Optional[str] = os.getenv("GITLAB_TOKEN")

    # GitHub configuration
    GITHUB_URL: str = "https://github.com"
    GITHUB_TOKEN: Optional[str] = os.getenv("GITHUB_TOKEN")

    # LLM Providers
    OPENAI_API_KEY: Optional[str] = None
    GROQ_API_KEY: Optional[str] = None
    OLLAMA_BASE_URL: str = "http://localhost:11434"

    # Default LLM provider
    DEFAULT_LLM_PROVIDER: str = "openai"

    # File operations
    WORKSPACE_DIR: str = os.getcwd()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
    )


def get_settings() -> Settings:
    """Get application settings."""
    return Settings()
