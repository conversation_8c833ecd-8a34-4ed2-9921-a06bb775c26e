from app.templates.prompts import BASE_PROMPT, SEARCH_BASE_PROMPT, LIST_BASE_PROMPT, FETCH_BASE_PROMPT, DELETE_BASE_PROMPT

# --- Common Structures ---


JS_BASE_STRUCTURE = """
src/
├── config/
│   └── database.js           # DB Connection Setup ({db_orm} + {db_type})
├── controllers/
│   └── [resource].controller.js # Handling Request and Response
├── {dao_or_repo}/             # Data Access Layer ({dao_or_repo_name})
│   └── [resource].{dao_or_repo_short}.js
├── logger/
│   └── logger.js             # Winston logger configuration
├── models/
│   └── [resource].model.js   # Data Model ({db_orm} model/schema)
├── routes/
│   └── [resource].routes.js  # Handling Route Definitions
├── services/
│   └── [resource].service.js # Handling business logic
├── middleware/
│   ├── validation.middleware.js # Request validation
│   ├── error.middleware.js   # Error handling
│   └── auth.middleware.js    # Authentication middleware
├── utils/
│   └── helpers.js            # Utility functions
{ddl_structure}
├── .env                      # Environment variables for both dev & prod
├── .gitignore                # Git ignore file
├── README.md                 # README file
├── package.json              # Node.js dependencies
├── jest.config.js            # Jest testing configuration
└── server.js                 # Server startup File
"""

NEXTJS_BASE_STRUCTURE = """
src/
├── app/                      # Next.js App Router
│   ├── api/                  # API Routes
│   │   └── [resource]/       # Resource-specific API endpoints
│   │       ├── route.ts      # API route handlers (TypeScript)
│   │       └── [...]/        # Dynamic API routes
│   ├── [resource]/           # Resource pages
│   │   ├── page.tsx          # List page (React component with TypeScript)
│   │   ├── [id]/             # Dynamic routes
│   │   │   └── page.tsx      # Detail page (React component with TypeScript)
│   │   └── layout.tsx        # Layout for resource pages
│   └── layout.tsx            # Root layout
├── components/               # Reusable UI components
│   ├── ui/                   # Basic UI components
│   └── [resource]/           # Resource-specific components
├── lib/                      # Shared utilities
│   ├── db/                   # Database utilities
│   │   └── {db_type}.ts      # {db_type} client (TypeScript)
│   ├── api/                  # API utilities
│   │   └── [resource].ts     # Resource-specific API functions
│   └── utils/                # Helper functions
├── models/                   # Data models
│   └── [resource].ts         # {db_orm} models/schemas with TypeScript interfaces
├── services/                 # Business logic
│   └── [resource].service.ts # Resource-specific service
├── middleware/               # Custom middleware
│   └── auth.ts               # Authentication middleware
├── types/                    # TypeScript type definitions
│   └── index.ts              # Shared types and interfaces
{ddl_structure}
├── .env                      # Environment variables
├── .env.local                # Local environment variables
├── .gitignore                # Git ignore file
├── README.md                 # README file
├── package.json              # Node.js dependencies
├── next.config.js            # Next.js configuration
├── jest.config.js            # Jest testing configuration
├── tsconfig.json             # TypeScript configuration
└── next-env.d.ts             # Next.js TypeScript declarations
"""
NESTJS_BASE_STRUCTURE = """
src/
├── app.module.ts             # Root application module
├── app.controller.ts         # Root controller
├── app.service.ts            # Root service
├── main.ts                   # Application entry point
├── config/
│   └── database.config.ts    # Database configuration
├── [resource]/               # Resource module
│   ├── dto/                  # Data Transfer Objects
│   │   ├── create-[resource].dto.ts
│   │   └── update-[resource].dto.ts
│   ├── entities/             # Database entities
│   │   └── [resource].entity.ts
│   ├── [resource].module.ts  # Resource module definition
│   ├── [resource].controller.ts # Resource controller
│   ├── [resource].service.ts # Resource service
│   └── [resource].repository.ts # Resource repository (optional)
├── common/                   # Shared code
│   ├── decorators/           # Custom decorators
│   ├── filters/              # Exception filters
│   ├── guards/               # Route guards
│   ├── interceptors/         # Request/response interceptors
│   ├── middleware/           # Custom middleware
│   └── pipes/                # Validation pipes
├── utils/                    # Utility functions
│   └── helpers.ts            # Helper functions
{ddl_structure}
├── .env                      # Environment variables
├── .gitignore                # Git ignore file
├── README.md                 # README file
├── package.json              # Node.js dependencies
├── nest-cli.json             # NestJS CLI configuration
├── tsconfig.json             # TypeScript configuration
├── tsconfig.build.json       # TypeScript build configuration
└── jest.config.js            # Jest testing configuration
"""

TS_BASE_STRUCTURE = """
src/
├── config/
│   └── database.ts           # DB Connection Setup ({db_orm} + {db_type})
├── secrets/
│   └── secrets.manager.ts    # Handles loading from secret manager
├── controllers/
│   └── [resource].controller.ts # Handling Request and Response
├── {dao_or_repo}/             # Data Access Layer ({dao_or_repo_name})
│   └── [resource].{dao_or_repo_short}.ts
├── logger/
│   └── logger.ts             # Winston logger configuration
├── models/
│   └── [resource].model.ts   # Data Model ({db_orm} model/schema)
├── routes/
│   └── [resource].routes.ts  # Handling Route Definitions
├── schemas/
│   └── [resource].schema.ts  # TypeScript interfaces (DTOs)
├── services/
│   └── [resource].service.ts # Handling business logic
{ddl_structure}
├── .env                      # Environment variables for both dev & prod
├── .gitignore                # Git ignore file
├── README.md                 # README file
├── package.json              # Node.js dependencies, extremely verbose
├── tsconfig.json             # TypeScript configuration
├── app.ts                    # Main Application / Express Setup
└── server.ts                 # Server startup File
"""

PYTHON_BASE_STRUCTURE = """
app/
├── config/
│   └── database.py           # DB Connection Setup ({db_orm} + {db_type})
├── secrets/
│   └── secrets_manager.py    # Handles loading from secret manager
├── controllers/
│   └── [resource]_controller.py # Handling Request and Response
├── repositories/             # Data Access Layer
│   └── [resource]_repository.py
├── logger/
│   └── logger.py             # Logging configuration
├── models/
│   └── [resource].py         # Data Model ({db_orm} model)
├── routes/
│   └── [resource]_routes.py  # Handling Route Definitions
├── schemas/
│   └── [resource]_schema.py  # Pydantic models
├── services/
│   └── [resource]_service.py # Handling business logic
{ddl_structure}
├── .env                      # Environment variables for both dev & prod
├── .gitignore                # Git ignore file
├── README.md                 # README file
├── requirements.txt          # Python dependencies
└── main.py                   # FastAPI app setup and startup
"""

# --- Common Requirements ---

COMMON_JS_REQUIREMENTS = """
1. Provide complete, executable code for all components.
2. Use a single shared DB connection instance/pool across layers.
3. Implement proper error handling with try/catch blocks.
4. Separate logic into controllers (HTTP), services (business rules), and data access layer.
5. Return appropriate HTTP status codes (404, 400, 500) for errors.
6. Ensure REST compliance with resource-based URLs, proper HTTP methods, and standard codes.
7. Log actions/errors with Winston.
8. Document all endpoints with JSDoc comments including parameters, responses, and examples.
9. Structure scalable routes using Express Router (e.g., /users, /products).
10. Validate inputs using middleware (e.g., express-validator, joi).
11. Implement security best practices (input validation, parameterized queries, CORS, etc.).
12. Use environment variables for configuration (database credentials, API keys, etc.).
"""

COMMON_NEXTJS_REQUIREMENTS = """
1. Follow Next.js App Router best practices and conventions.
2. Implement proper error handling with try/catch blocks and error boundaries.
3. Use React Server Components (RSC) where appropriate for improved performance.
4. Implement client-side components with 'use client' directive where needed.
5. Create reusable UI components in the components directory.
6. Implement proper data fetching strategies (Server Components, SWR, or React Query).
7. Use Next.js API routes for backend functionality with proper error handling.
8. Implement proper form validation using libraries like zod, yup, or react-hook-form.
9. Follow security best practices (input validation, CSRF protection, etc.).
10. Use environment variables for configuration (.env.local for development).
11. Implement responsive design using CSS modules, Tailwind CSS, or styled-components.
12. Add proper SEO metadata using Next.js metadata API.
13. Implement proper loading and error states for better UX.
"""

COMMON_NESTJS_REQUIREMENTS = """
1. Follow NestJS module architecture and dependency injection patterns.
2. Use NestJS decorators for controllers, services, modules, and request handling.
3. Implement proper DTOs (Data Transfer Objects) for request validation.
4. Use class-validator and class-transformer for input validation.
5. Implement proper error handling with NestJS exception filters.
6. Use NestJS guards for authentication and authorization.
7. Implement proper logging with NestJS built-in logger or custom logger.
8. Document APIs with Swagger using NestJS/Swagger decorators.
9. Follow repository pattern for database operations.
10. Use TypeORM or Mongoose for database interactions.
11. Implement proper environment configuration using NestJS ConfigModule.
12. Use NestJS interceptors for response transformation and caching.
13. Implement proper testing with Jest and NestJS testing utilities.
"""

COMMON_TS_REQUIREMENTS = """
1. Enforce strict typing with TypeScript, avoiding `any`. Use interfaces/types for DTOs.
2. Provide complete, executable code for all components.
3. Use a single shared DB connection instance/pool across layers.
4. Expose typed interfaces for integration at all layers (controllers, services, data access).
5. Separate logic into controllers (HTTP), services (business rules), and data access layer.
6. Return appropriate HTTP status codes (404, 400, 500) for errors.
7. Ensure REST compliance with resource-based URLs, proper HTTP methods, and standard codes.
8. Log actions/errors with Winston.
9. Annotate all endpoints with Swagger/OpenAPI for parameters, responses, and examples.
10. Structure scalable routes using Express Router (e.g., /users, /products).
11. Validate types across layers (e.g., convert/verify request param strings to numbers).
"""

COMMON_PYTHON_REQUIREMENTS = """
1. Use proper type annotations throughout with Python type hints. Use Pydantic for validation.
2. Provide complete, executable code for all components.
3. Use a single shared DB session factory/client across repositories.
4. Use Pydantic for request/response validation and serialization.
5. Separate logic into controllers (HTTP), services (business rules), and repositories (database).
6. Return appropriate HTTP status codes (404, 400, 500) for errors.
7. Ensure REST compliance with resource-based URLs, proper HTTP methods, and standard codes.
8. Use DB transactions for atomicity and log actions/errors.
9. Use FastAPI's built-in OpenAPI for documenting endpoints, parameters, responses.
10. Structure scalable routes using FastAPI's APIRouter.
11. Use FastAPI dependency injection for services and repositories.
12. Use FastAPI exception handlers for returning appropriate error responses.
13. Implement proper logging with Python's logging module at all layers.
"""

COMMON_JSDOC_REQUIREMENT = """
**CRUCIAL: Add JSDoc comments for every exported function, class, method, and controller route.**
Each JSDoc block must include:
- A brief summary of what the function/class does
- `@param` tags for all parameters, with types and descriptions
- `@returns` tag describing the return type and purpose
- `@throws` or `@error` tag for expected error conditions (if any)

Follow this structure for JSDoc:
/**
 * [Brief summary of the function's purpose]
 *
 * @param {{Type}} paramName - Description
 * @returns {{ReturnType}} - Description
 * @throws {{ErrorType}} - Description of the error
 */
Include JSDoc for Controller methods, Service methods, DAO/Repository methods, Models, Config/utility functions, Route handlers.
"""

COMMON_PYTHON_DOCSTRING_REQUIREMENT = """
**CRUCIAL: Add comprehensive docstrings for all functions, classes, and methods.**
Use a standard format (like Google or NumPy style). Each docstring must include:
- A brief summary line.
- A more detailed explanation (if needed).
- Args: section describing each parameter (name, type, description).
- Returns: section describing the return value (type, description).
- Raises: section describing potential exceptions.

Example:
\"\"\"[Brief summary]

[Detailed explanation if needed]

Args:
    param1 (str): Description of param1.
    param2 (int): Description of param2.

Returns:
    bool: Description of return value.

Raises:
    ValueError: If param1 is invalid.
\"\"\"
Include docstrings for: Controller methods, Service methods, Repository methods, Models, Config/utility functions.
"""

# --- Database/ORM Specific Details ---

DB_DETAILS = {
    "mysql": {"orm": "SQLAlchemy", "ddl": True, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "postgresql": {"orm": "SQLAlchemy", "ddl": True, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "mongodb": {"orm": "PyMongo/MongoEngine", "ddl": False, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "typescript_mysql": {"orm": "Sequelize", "ddl": True, "dao_or_repo": "dao", "dao_or_repo_short": "dao", "dao_or_repo_name": "DAO"},
    "typescript_postgresql": {"orm": "Sequelize", "ddl": True, "dao_or_repo": "dao", "dao_or_repo_short": "dao", "dao_or_repo_name": "DAO"},
    "typescript_mongodb": {"orm": "Mongoose", "ddl": False, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "javascript_mysql": {"orm": "Sequelize", "ddl": True, "dao_or_repo": "dao", "dao_or_repo_short": "dao", "dao_or_repo_name": "DAO"},
    "javascript_postgresql": {"orm": "Sequelize", "ddl": True, "dao_or_repo": "dao", "dao_or_repo_short": "dao", "dao_or_repo_name": "DAO"},
    "javascript_mongodb": {"orm": "Mongoose", "ddl": False, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "nextjs_mysql": {"orm": "Prisma", "ddl": True, "dao_or_repo": "lib/db", "dao_or_repo_short": "db", "dao_or_repo_name": "Database Client"},
    "nextjs_postgresql": {"orm": "Prisma", "ddl": True, "dao_or_repo": "lib/db", "dao_or_repo_short": "db", "dao_or_repo_name": "Database Client"},
    "nextjs_mongodb": {"orm": "Mongoose", "ddl": False, "dao_or_repo": "lib/db", "dao_or_repo_short": "db", "dao_or_repo_name": "Database Client"},
    "nestjs_mysql": {"orm": "TypeORM", "ddl": True, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "nestjs_postgresql": {"orm": "TypeORM", "ddl": True, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
    "nestjs_mongodb": {"orm": "Mongoose", "ddl": False, "dao_or_repo": "repositories", "dao_or_repo_short": "repository", "dao_or_repo_name": "Repository"},
}



# --- Template-Specific Requirements (Database Specific Parts Only) ---

# General CRUD / Add API
TEMPLATES_SPECIFIC = {
    "typescript_mysql": """
REQUIREMENTS_SPECIFIC:
- Use Sequelize for database operations with MySQL.
- Include necessary MySQL DDL scripts.
- Use Jest for testing with appropriate mocks. Include comprehensive unit, integration, and E2E tests.
""",
    "typescript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use Sequelize for database operations with PostgreSQL.
- Use PostgreSQL-specific features like JSONB when appropriate.
- Include necessary PostgreSQL DDL scripts.
""",
    "typescript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations.
- Implement proper schema validation and indexing.
- Use MongoDB-specific features when applicable.
""",
    "python_mysql": """
REQUIREMENTS_SPECIFIC:
- Use SQLAlchemy for MySQL database operations.
- Include necessary MySQL DDL scripts.
""",
    "python_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use SQLAlchemy for PostgreSQL database operations.
- Use PostgreSQL-specific features like JSONB when appropriate.
- Include necessary PostgreSQL DDL scripts.
""",
    "python_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use PyMongo or MongoEngine for MongoDB operations.
- Implement proper schema validation and indexing.
- Use MongoDB-specific features when applicable.
""",
    # JavaScript
    "javascript_mysql": """
REQUIREMENTS_SPECIFIC:
- Use Sequelize for database operations with MySQL.
- Include necessary MySQL DDL scripts.
- Use Jest for testing with appropriate mocks.
- Implement proper error handling with try/catch blocks.
""",
    "javascript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use Sequelize for database operations with PostgreSQL.
- Use PostgreSQL-specific features like JSONB when appropriate.
- Include necessary PostgreSQL DDL scripts.
- Implement proper error handling with try/catch blocks.
""",
    "javascript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations.
- Implement proper schema validation and indexing.
- Use MongoDB-specific features like aggregation pipelines when applicable.
- Implement proper error handling with try/catch blocks.
""",

    # Next.js
    "nextjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Use Prisma ORM for MySQL database operations.
- Implement proper data fetching in API routes.
- Use React Server Components for data fetching where appropriate.
- Implement proper client-side form validation.
- Add appropriate loading and error states.
""",
    "nextjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use Prisma ORM for PostgreSQL database operations.
- Use PostgreSQL-specific features like JSONB when appropriate.
- Implement proper data fetching in API routes.
- Use React Server Components for data fetching where appropriate.
- Implement proper client-side form validation.
- Add appropriate loading and error states.
""",
    "nextjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations.
- Implement proper schema validation and indexing.
- Use MongoDB-specific features like aggregation pipelines when applicable.
- Implement proper data fetching in API routes.
- Use React Server Components for data fetching where appropriate.
- Implement proper client-side form validation.
- Add appropriate loading and error states.
""",

    # NestJS
    "nestjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Use TypeORM for MySQL database operations.
- Include necessary MySQL entity definitions.
- Implement proper dependency injection.
- Use NestJS decorators for controllers, services, and modules.
- Implement proper validation using class-validator and class-transformer.
- Document API with Swagger using NestJS decorators.
""",
    "nestjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use TypeORM for PostgreSQL database operations.
- Use PostgreSQL-specific features like JSONB when appropriate.
- Include necessary PostgreSQL entity definitions.
- Implement proper dependency injection.
- Use NestJS decorators for controllers, services, and modules.
- Implement proper validation using class-validator and class-transformer.
- Document API with Swagger using NestJS decorators.
""",
    "nestjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations with NestJS Mongoose module.
- Implement proper schema validation and indexing.
- Use MongoDB-specific features like aggregation pipelines when applicable.
- Implement proper dependency injection.
- Use NestJS decorators for controllers, services, and modules.
- Implement proper validation using class-validator and class-transformer.
- Document API with Swagger using NestJS decorators.
""",
}

# Delete API
DELETE_TEMPLATE_SPECIFIC = {
    "typescript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL CREATE TABLE scripts with id, createdAt, updatedAt, deletedAt.
- Define Sequelize models with timestamps: true, paranoid: true, and shared instance.
- Implement soft deletes with paranoid: true and retain deletedAt for auditability.
""",
    "typescript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL CREATE TABLE scripts with id, createdAt, updatedAt, deletedAt.
- Define Sequelize models with timestamps: true, paranoid: true, and shared instance.
- Implement soft deletes with paranoid: true and retain deletedAt for auditability.
- Leverage PostgreSQL-specific features like JSONB where appropriate.
""",
    "typescript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose schema with timestamps: true and softDelete functionality (isDeleted flag).
- Use MongoDB sessions for atomicity.
- Define Mongoose schemas with proper validation, timestamps and indexes.
- Implement soft deletes with isDeleted flag and track deletedAt for auditability.
- Leverage MongoDB-specific features like aggregation pipelines where appropriate.
""",
    "python_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL CREATE TABLE scripts with id, created_at, updated_at, deleted_at.
- Define SQLAlchemy models with proper relationship tracking and soft delete capability.
- Implement soft deletes and retain deleted_at for auditability.
""",
    "python_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL CREATE TABLE scripts with id, created_at, updated_at, deleted_at.
- Define SQLAlchemy models with proper relationship tracking and soft delete capability.
- Implement soft deletes and retain deleted_at for auditability.
- Leverage PostgreSQL-specific features like JSONB where appropriate.
""",
    "python_mongodb": """
REQUIREMENTS_SPECIFIC:
- Define PyMongo/MongoEngine models with soft delete capability (e.g., is_deleted flag).
- Implement soft deletes and retain deleted_at for auditability.
- Leverage MongoDB-specific features like aggregation pipelines where appropriate.
""",
    # JavaScript
    "javascript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL CREATE TABLE scripts with id, createdAt, updatedAt, deletedAt.
- Define Sequelize models with timestamps: true, paranoid: true, and shared instance.
- Implement soft deletes with paranoid: true and retain deletedAt for auditability.
""",
    "javascript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL CREATE TABLE scripts with id, createdAt, updatedAt, deletedAt.
- Define Sequelize models with timestamps: true, paranoid: true, and shared instance.
- Implement soft deletes with paranoid: true and retain deletedAt for auditability.
- Leverage PostgreSQL-specific features like JSONB where appropriate.
""",
    "javascript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose schema with timestamps: true and softDelete functionality (isDeleted flag).
- Define Mongoose schemas with proper validation, timestamps and indexes.
- Implement soft deletes with isDeleted flag and track deletedAt for auditability.
- Leverage MongoDB-specific features like aggregation pipelines where appropriate.
""",

    # Next.js
    "nextjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Use Prisma ORM for MySQL database operations with proper schema definitions.
- Include necessary model definitions with id, createdAt, updatedAt, deletedAt fields.
- Implement soft deletes using Prisma middleware or custom logic to set deletedAt.
""",
    "nextjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use Prisma ORM for PostgreSQL database operations with proper schema definitions.
- Include necessary model definitions with id, createdAt, updatedAt, deletedAt fields.
- Implement soft deletes using Prisma middleware or custom logic to set deletedAt.
""",
    "nextjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations with proper schema definitions.
- Define Mongoose schemas with timestamps: true and isDeleted flag for soft deletes.
- Implement soft deletes with isDeleted flag and track deletedAt for auditability.
""",

    # NestJS
    "nestjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Use TypeORM for MySQL database operations with proper entity definitions.
- Include necessary entity definitions with id, createdAt, updatedAt, deletedAt columns.
- Implement soft deletes using TypeORM's @DeleteDateColumn decorator.
""",
    "nestjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Use TypeORM for PostgreSQL database operations with proper entity definitions.
- Include necessary entity definitions with id, createdAt, updatedAt, deletedAt columns.
- Implement soft deletes using TypeORM's @DeleteDateColumn decorator.
""",
    "nestjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB operations with NestJS Mongoose module.
- Define Mongoose schemas with timestamps: true and isDeleted flag for soft deletes.
- Implement soft deletes with isDeleted flag and track deletedAt for auditability.
"""
}

# Fetch API
FETCH_TEMPLATE_SPECIFIC = {
    "typescript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "typescript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "typescript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "python_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Add security middleware (CORS, rate limiting, authentication).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "python_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Add security middleware (CORS, rate limiting, authentication).
- Handle edge cases (not found, invalid queries, validation errors).
- Leverage PostgreSQL-specific features like JSONB where appropriate.
""",
    "python_mongodb": """
REQUIREMENTS_SPECIFIC:
- Add security middleware (CORS, rate limiting, authentication).
- Handle edge cases (not found, invalid queries, validation errors).
- Leverage MongoDB-specific features like aggregation pipelines where appropriate.
""",
    # JavaScript
    "javascript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "javascript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
""",
    "javascript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Add security middleware (Helmet, CORS, rate limiting, CSP).
- Handle edge cases (not found, invalid queries, validation errors).
- Leverage MongoDB-specific features like aggregation pipelines where appropriate.
""",
    # Next.js
    "nextjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL schema definitions using Prisma.
- Implement data fetching in API routes with proper error handling.
- Use React Server Components for data fetching where appropriate.
""",
    "nextjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL schema definitions using Prisma.
- Implement data fetching in API routes with proper error handling.
- Use React Server Components for data fetching where appropriate.
""",
    "nextjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Define Mongoose schemas with proper validation and indexing.
- Implement data fetching in API routes with proper error handling.
- Use React Server Components for data fetching where appropriate.
""",
    # NestJS
    "nestjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL entity definitions using TypeORM.
- Use NestJS decorators for controllers, services, and modules.
- Document API with Swagger using NestJS decorators.
""",
    "nestjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL entity definitions using TypeORM.
- Use NestJS decorators for controllers, services, and modules.
- Document API with Swagger using NestJS decorators.
""",
    "nestjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Define Mongoose schemas with NestJS Mongoose module.
- Use NestJS decorators for controllers, services, and modules.
- Document API with Swagger using NestJS decorators.
"""
}

SEARCH_TEMPLATE_SPECIFIC = {
    "typescript_mysql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable in the schema. Map filters to parameterized SQL queries. For example: `name LIKE '%value%'` for partial match, `department = 'HR'` for exact match, `created_at >= '2023-01-01'` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total` in responses.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., class-validator or Joi) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant single-column indexes; prefer composite indexes for multi-field queries (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "typescript_postgresql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable in the schema. Map filters to parameterized SQL (or ORM) queries. For example: `name ILIKE '%value%'` for case-insensitive match, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total` in responses.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., class-validator or Joi) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). For PostgreSQL, consider GIN or BTREE indexes as appropriate. Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "typescript_mongodb": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on schema-defined fields. Map filters to MongoDB queries. For example: `{ name: /value/i }` for case-insensitive partial match, `{ department: 'HR' }` for exact match, `{ createdAt: { $gte: ISODate('2023-01-01') } }` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `.sort({ createdAt: -1 })` for descending. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `skip`. Default `limit = 10`, maximum `limit = 100`. Compute `skip = (page - 1) * limit`. Return `page`, `limit`, and `total` in responses.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject any queries on non-permitted fields (400). Use only analytics-enabled fields in aggregation pipelines.
- **Validation & Error Handling**: Use a validation library (e.g., Joi or class-validator) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer compound indexes for common multi-field queries (e.g., `db.collection.createIndex({ status:1, createdAt:-1 })`). For text searches, ensure appropriate text or regex indexes.
- **Response Format**: Return JSON:
  - `data`: array of documents.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "python_mysql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable in the schema. Map filters to parameterized SQL queries (e.g., using SQLAlchemy or raw queries). For example: `name LIKE '%value%'` for partial match, `department = 'HR'` for exact match, `created_at >= '2023-01-01'` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total` in responses.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject any filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Pydantic or Marshmallow) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant single-column indexes; prefer composite indexes for multi-field queries (e.g., `INDEX idx_status_date (status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON (dictionary) with keys:
  - `data`: list of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "python_postgresql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable in the schema. Map filters to parameterized SQL (or ORM) queries. For example: `name ILIKE '%value%'` for case-insensitive match, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Pydantic or Marshmallow) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). For PostgreSQL, consider GIN indexes for JSON or full-text fields. Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON (dictionary) with keys:
  - `data`: list of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "python_mongodb": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on schema-defined fields. Map filters to MongoDB queries. For example: `{'name': {'$regex': 'value', '$options': 'i'}}` for case-insensitive partial match, `{'department': 'HR'}` for exact match, `{'createdAt': {'$gte': datetime(2023,1,1)}}` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `cursor.sort('createdAt', -1)` for descending. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `skip`. Default `limit = 10`, maximum `limit = 100`. Compute `skip = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject any queries on non-permitted fields (400). Use only analytics-enabled fields in aggregation pipelines.
- **Validation & Error Handling**: Use a validation library (e.g., Pydantic or Marshmallow) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer compound indexes (e.g., `db.collection.create_index([('status', 1), ('createdAt', -1)])`). Ensure indexes on fields frequently used in filters or sorts.
- **Response Format**: Return JSON (dict) with keys:
  - `data`: list of document objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "javascript_mysql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. Map filters to parameterized SQL queries (e.g., using a query builder or ORM). For example: `name LIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Joi or express-validator) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant single-column indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "javascript_postgresql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. Map filters to parameterized SQL (or ORM) queries. For example: `name ILIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Joi or express-validator) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). For PostgreSQL, consider GIN indexes for JSON or full-text fields. Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "javascript_mongodb": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on schema-defined fields. Map filters to MongoDB queries (e.g., using Mongoose or Mongo client). For example: `{ name: { $regex: 'value', $options: 'i' } }` for case-insensitive partial match, `{ department: 'HR' }` for exact match, `{ createdAt: { $gte: ISODate('2023-01-01') } }` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `.sort({ createdAt: -1 })` for descending. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `skip`. Default `limit = 10`, maximum `limit = 100`. Compute `skip = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject any queries on non-permitted fields (400). Use only analytics-enabled fields in aggregation pipelines.
- **Validation & Error Handling**: Use a validation library (e.g., Joi) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer compound indexes (e.g., `db.collection.createIndex({ status:1, createdAt:-1 })`). Ensure indexes on fields frequently used in filters or sorts.
- **Response Format**: Return JSON with keys:
  - `data`: array of document objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nextjs_mysql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. In a Next.js API route, map filters to SQL queries (using Prisma, Sequelize, or raw queries). For example: `name LIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` from schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Zod or Joi) to validate query params in the API route. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant single-column indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nextjs_postgresql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. In a Next.js API route, map filters to SQL queries. For example: `name ILIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` from schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., Zod or Joi) to validate query params. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). For PostgreSQL, consider GIN indexes for JSON or full-text fields. Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nextjs_mongodb": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on schema-defined fields. In a Next.js API route, map filters to MongoDB queries. For example: `{ name: { $regex: 'value', $options: 'i' } }`, `{ department: 'HR' }`, `{ createdAt: { $gte: ISODate('2023-01-01') } }`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `.sort({ createdAt: -1 })` for descending. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `skip`. Default `limit = 10`, maximum `limit = 100`. Compute `skip = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` from schema metadata. Ignore or reject any queries on non-permitted fields (400). Use only analytics-enabled fields in aggregation pipelines.
- **Validation & Error Handling**: Use a validation library (e.g., Zod or Joi) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer compound indexes (e.g., `db.collection.createIndex({ status:1, createdAt:-1 })`). Ensure indexes on fields frequently used in filters or sorts.
- **Response Format**: Return JSON with keys:
  - `data`: array of document objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nestjs_mysql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. In NestJS (with TypeORM/Sequelize), map filters to ORM queries or parameterized SQL. For example: `name LIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use NestJS validation pipes (class-validator) to validate query params. Return HTTP 400 for invalid inputs with detailed errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `INDEX idx_status_date (status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nestjs_postgresql": """REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on fields marked filterable. In NestJS (with TypeORM), map filters to SQL queries. For example: `name ILIKE '%value%'`, `department = 'HR'`, `created_at >= '2023-01-01'`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use NestJS validation pipes (class-validator) to validate query params. Return HTTP 400 for invalid inputs with detailed errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer composite indexes (e.g., `INDEX idx_status_date (status, created_at)`). For PostgreSQL, consider GIN indexes for JSON or full-text fields. Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON with keys:
  - `data`: array of record objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",

    "nestjs_mongodb":
"""REQUIREMENTS_SPECIFIC:
- **Filtering**: Support filters only on schema-defined fields. In NestJS (with Mongoose), map filters to MongoDB queries. For example: `{ name: { $regex: 'value', $options: 'i' } }`, `{ department: 'HR' }`, `{ createdAt: { $gte: ISODate('2023-01-01') } }`.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `.sort({ createdAt: -1 })` for descending. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `skip`. Default `limit = 10`, maximum `limit = 100`. Compute `skip = (page - 1) * limit`. Return `page`, `limit`, and `total`.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject any queries on non-permitted fields (400). Use only analytics-enabled fields in aggregation pipelines.
- **Validation & Error Handling**: Use NestJS validation pipes (class-validator) to validate query params. Return HTTP 400 for invalid inputs with detailed errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant indexes; prefer compound indexes (e.g., `db.collection.createIndex({ status:1, createdAt:-1 })`). Ensure indexes on fields frequently used in filters or sorts.
- **Response Format**: Return JSON with keys:
  - `data`: array of document objects.
  - `pagination`: object with `total`, `limit`, `page`.
  - `analytics`: object with any aggregate info (if applicable).
  - `errors`: list of error details (if any).
Example: `{"data":[...],"pagination":{"total":50,"limit":10,"page":1},"analytics":{}}`.""",
}


# # Search API
# SEARCH_TEMPLATE_SPECIFIC = {
#     "typescript_mysql": """
# REQUIREMENTS_SPECIFIC:
# - Include MySQL DDL script.
# - Support dynamic filters: exact match, partial text, range, and enums (`IN`).
# - Build dynamic `WHERE` clauses using Sequelize options in the DAO.
# - Add pagination using `limit` and `offset`, with defaults (`10`, `0`).
# - Enable sorting via `sortBy` and `order` for allowed fields.
# - Return filtered results with aggregated statistical metadata.
# - Include computed analytics (e.g., grouped counts, derived values) in the search response when requested in the prompt.
# - Use `Sequelize.fn()` and `Sequelize.literal()` for aggregate queries.
# - Use raw SQL or `Sequelize.query()` in DAO for performance-critical analytics.
# - Add indexes on frequently filtered and sorted fields. Avoid indexing low-cardinality fields unless part of composite keys.
# - Use Sequelize prepared statements to prevent SQL injection.
# - Validate and sanitize inputs using `zod`, `joi`, etc.
# - Return response as: { data: [...], pagination: { limit, offset, total }, analytics: { ... } }
# - Implement: SearchService.search(filters, limit, offset, sortBy, order): Promise<{ data; pagination; analytics }>;
# - Infer `filterable`, `sortable`, `analyticsFields` dynamically from schema.
# """,
#     "typescript_postgresql": """
# REQUIREMENTS_SPECIFIC:
# - Include PostgreSQL DDL script.
# - Support flexible filtering: exact match, prefix search, enums, and date/number filters.
# - Build dynamic `WHERE` clauses using ORM query builders (Sequelize.Op or TypeORM.QueryBuilder).
# - Support pagination with `LIMIT` and `OFFSET` (default values and max limits). Optionally, support cursor-based pagination.
# - Enable sorting with `sortBy` and `order` parameters (ASC | DESC), ensuring only whitelisted fields are sortable.
# - Include computed analytics (e.g., grouped counts, aggregates) in the search response when requested in the prompt.
# - DO NOT create separate analytics endpoints - include analytics in the main search response.
# - Use ORM functions like `Sequelize.fn`, `Sequelize.literal`, or `TypeORM.raw` for performance.
# - Add indexes for performance on frequently searched fields and composite indexes for combined filters. Use **GIN indexes** for full-text search where needed.
# - Use parameterized queries through ORM to avoid SQL injection.
# - Validate and sanitize inputs using `zod`, `class-validator`, etc., ensuring proper formatting.
# - Follow DRY principles, abstracting query-building logic (e.g., `buildSearchQuery()` helper).
# - Use `dotenv` for all configuration, including DB URLs and pagination defaults (e.g., `SEARCH_MAX_LIMIT=100`).
# """,
#     "typescript_mongodb": """
# REQUIREMENTS_SPECIFIC:
# - Accept dynamic filters via query strings using regex, `$in`, `$gte`, etc.
# - Centralize filtering logic (e.g., `buildFilterQuery()`); validate filters via schema or whitelist.
# - Support `limit` and `skip` with sane defaults and caps; use cursor pagination optionally.
# - Allow sorting via `sortBy` and `order` with validation for indexed fields.
# - Include analytics in the search response when requested in the prompt - DO NOT create separate analytics endpoints.
# - Use aggregation pipelines (`$group`, `$expr`, etc.) for analytics within the search response.
# - Sanitize and validate all query params to prevent NoSQL injection.
# - Use MongoDB transactions with session-based logic for multi-doc operations.
# - Add indexes on frequently queried/sorted fields; use compound/partial/TTL indexes as needed.
# - Monitor query performance using MongoDB profiler; log slow queries and errors with context.
# - Rate-limit sensitive routes using `express-rate-limit` or similar, with env-based configs.
# - Load environment configs with `dotenv` for DB URI, caps, logging, and rate limits.
# - Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
# """,
#     "python_mysql": """
# REQUIREMENTS_SPECIFIC:
# - Include MySQL DDL script.
# - Support dynamic filters for `id` (exact), `name` (prefix), `gender` (enum), and `years_completed` (date math via `TIMESTAMPDIFF`).
# - Centralize filter and query construction logic in `base_repository.py` for reuse.
# - Implement pagination via `.limit()` and `.offset()` with a configurable max cap.
# - Support sorting with SQLAlchemy's `.order_by()` using `sort_by` and `order` query params.
# - Create indexes on required fields (`name`, `gender`, `doj`) and composite indexes for combined filters to optimize searches.
# - Include analytics in the search response when requested in the prompt - DO NOT create separate analytics endpoints.
# - Use parameterized queries and validate inputs with Pydantic to prevent SQL injection.
# - Define the `employees` table with `id`, `name`, `gender`, `doj`, `department`, and required indexes.
# - Load DB credentials, pagination defaults, and app configs from `.env` using `python-dotenv`.
# - Use consistent API response schemas for `success`, `data`, and `error`.
# - Enable rate limiting using `slowapi` or custom middleware to prevent API abuse.
# """,
#     "python_postgresql": """
# REQUIREMENTS_SPECIFIC:
# - Include PostgreSQL DDL script.
# - Use SQLAlchemy 2.0 with asyncpg engine.
# - Define the `resources` table with typed columns, constraints, and indexes (GIN, trigram, composite).
# - Enable Postgres `pg_trgm` extension for fuzzy `ILIKE` and partial `department` matching.
# - Build dynamic filters in the repository for `id`, `name`, `gender`, `department`, and `years_employed` using Postgres date math.
# - Implement pagination using `.limit().offset()` or keyset pagination for large datasets.
# - Support dynamic sorting with `sort_by` and `order` using SQLAlchemy’s `.order_by()` clause.
# - Include analytics in the search response when requested in the prompt - DO NOT create separate analytics endpoints.
# - Sanitize all user inputs, especially for `ILIKE`, and use only parameterized SQL queries.
# - Log slow queries over `QUERY_TIMEOUT_MS` with `EXPLAIN ANALYZE` via a custom query logger.
# - Load environment configs like DB URI, timeout caps, and search defaults from `.env`.
# - Use consistent API response format with `{ success, data, error }` in all endpoints.
# - Apply rate limiting middleware (`slowapi`) to protect high-traffic routes.
# - Generate complete Swagger (OpenAPI 3.0) documentation in 'docs/openapi.yaml'.
# """,
#     "python_mongodb": """
# REQUIREMENTS_SPECIFIC:
# - Use Motor/Beanie for async MongoDB access.
# - Define `resources` schema with `_id`, `name`, `gender`, `doj` (ISODate), `department`, `status`, and optionally `is_deleted`.
# - Apply filters for `_id` (validate ObjectId), `name` (`$regex`, case-insensitive), `gender`, `department`, and `doj` with date math.
# - Implement pagination with `.limit().skip()` or keyset (using `_id`), and dynamic `.sort()` for sort field and order.
# - Create indexes on `name`, `doj`, and composite fields like `{ department, status }` and enable full-text if needed.
# - Include analytics in the search response when requested in the prompt - DO NOT create separate analytics endpoints.
# - Escape and validate regex inputs, use Motor’s async-safe operations, and guard inputs to prevent NoSQL injection.
# - Only enable MongoDB transactions if connected to a replica set or sharded cluster.
# - Configure timeouts and pooling in Motor client for stability under load.
# - Load env vars (e.g., DB URI, default pagination limits) from `.env` using `python-dotenv`.
# - Log slow queries in dev using `.explain()` and monitor query latency via MongoDB profiler.
# - Format API responses consistently with `{ success, data, error }` structure.
# - Add rate limiting or throttling (e.g., via `slowapi`) to protect public-facing endpoints.
# """,
#     # JavaScript entries
#     "javascript_mysql": """
# REQUIREMENTS_SPECIFIC:
# - Include MySQL DDL script.
# - Support dynamic filters: exact match, partial text, range, and enums (`IN`).
# - Build dynamic `WHERE` clauses using Sequelize options in the DAO.
# - Add pagination using `limit` and `offset`, with defaults (`10`, `0`).
# - Enable sorting via `sortBy` and `order` for allowed fields.
# - Include computed analytics in the search response when requested in the prompt.
# - Use `Sequelize.fn()` and `Sequelize.literal()` for aggregate queries.
# - Add indexes on frequently filtered and sorted fields.
# - Use Sequelize prepared statements to prevent SQL injection.
# - Validate and sanitize inputs using `joi` or similar libraries.
# - Return response as: { data: [...], pagination: { limit, offset, total }, analytics: { ... } }
# - Use `dotenv` for configuration, including DB URLs and pagination defaults.
# - Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
# """,
#     "javascript_postgresql": """
# REQUIREMENTS_SPECIFIC:
# - Include PostgreSQL DDL script.
# - Support flexible filtering: exact match, prefix search, enums, and date/number filters.
# - Build dynamic `WHERE` clauses using Sequelize query builders.
# - Support pagination with `LIMIT` and `OFFSET` with default values.
# - Enable sorting with `sortBy` and `order` parameters for whitelisted fields.
# - Include computed analytics in the search response when requested in the prompt.
# - Use Sequelize functions for performance-optimized queries.
# - Add indexes for performance on frequently searched fields and composite indexes.
# - Use parameterized queries through ORM to avoid SQL injection.
# - Validate and sanitize inputs using `joi` or similar libraries.
# - Follow DRY principles, abstracting query-building logic.
# - Use `dotenv` for configuration, including DB URLs and pagination defaults.
# - Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
# """,
#     "javascript_mongodb": """
# REQUIREMENTS_SPECIFIC:
# - Accept dynamic filters via query strings using regex, `$in`, `$gte`, etc.
# - Centralize filtering logic; validate filters via schema or whitelist.
# - Support `limit` and `skip` with sane defaults and caps.
# - Allow sorting via `sortBy` and `order` with validation for indexed fields.
# - Include analytics in the search response when requested in the prompt.
# - Use aggregation pipelines for analytics within the search response.
# - Sanitize and validate all query params to prevent NoSQL injection.
# - Add indexes on frequently queried/sorted fields.
# - Monitor query performance; log slow queries and errors with context.
# - Rate-limit sensitive routes using `express-rate-limit` or similar.
# - Load environment configs with `dotenv` for DB URI and other settings.
# - Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
# """,
#     # Next.js entries
#     "nextjs_mysql": """
# REQUIREMENTS_SPECIFIC:
# - Include MySQL schema definitions using Prisma.
# - Support dynamic filters with Prisma's where clause builder.
# - Implement pagination with take/skip and return total count.
# - Enable sorting with orderBy for whitelisted fields.
# - Include computed analytics when requested in the prompt.
# - Implement server-side and client-side validation.
# - Use React Server Components for data fetching where appropriate.
# - Add loading and error states for search operations.
# - Use parameterized queries through Prisma to avoid SQL injection.
# - Add indexes on frequently filtered and sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use environment variables for configuration settings.
# """,
#     "nextjs_postgresql": """
# REQUIREMENTS_SPECIFIC:
# - Include PostgreSQL schema definitions using Prisma.
# - Support dynamic filters with Prisma's where clause builder.
# - Implement pagination with take/skip and return total count.
# - Enable sorting with orderBy for whitelisted fields.
# - Include computed analytics when requested in the prompt.
# - Use PostgreSQL-specific features like full-text search where appropriate.
# - Use React Server Components for data fetching where appropriate.
# - Add loading and error states for search operations.
# - Use parameterized queries through Prisma to avoid SQL injection.
# - Add indexes on frequently filtered and sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use environment variables for configuration settings.
# """,
#     "nextjs_mongodb": """
# REQUIREMENTS_SPECIFIC:
# - Define Mongoose schemas with proper validation and indexing.
# - Support dynamic filters using MongoDB query operators.
# - Implement pagination using limit and skip with defaults.
# - Enable sorting for allowed fields with proper indexing.
# - Include computed analytics using MongoDB aggregation pipeline.
# - Implement server-side and client-side validation.
# - Use React Server Components for data fetching where appropriate.
# - Add loading and error states for search operations.
# - Sanitize and validate all query params to prevent NoSQL injection.
# - Add indexes on frequently queried/sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use environment variables for configuration settings.
# """,
#     # NestJS entries
#     "nestjs_mysql": """
# REQUIREMENTS_SPECIFIC:
# - Include MySQL entity definitions using TypeORM.
# - Create DTOs for search parameters with validation.
# - Build dynamic queries using TypeORM's QueryBuilder.
# - Implement pagination with proper metadata.
# - Enable sorting for whitelisted fields.
# - Include computed analytics in the search response when requested.
# - Use NestJS decorators for controllers, services, and modules.
# - Document API with Swagger using NestJS decorators.
# - Use parameterized queries through TypeORM to avoid SQL injection.
# - Add indexes on frequently filtered and sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use ConfigService for environment-based configuration.
# """,
#     "nestjs_postgresql": """
# REQUIREMENTS_SPECIFIC:
# - Include PostgreSQL entity definitions using TypeORM.
# - Create DTOs for search parameters with validation.
# - Build dynamic queries using TypeORM's QueryBuilder.
# - Implement pagination with proper metadata.
# - Enable sorting for whitelisted fields.
# - Include computed analytics in the search response when requested.
# - Use PostgreSQL-specific features like full-text search where appropriate.
# - Document API with Swagger using NestJS decorators.
# - Use parameterized queries through TypeORM to avoid SQL injection.
# - Add indexes on frequently filtered and sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use ConfigService for environment-based configuration.
# """,
#     "nestjs_mongodb": """
# REQUIREMENTS_SPECIFIC:
# - Define Mongoose schemas with NestJS Mongoose module.
# - Create DTOs for search parameters with validation.
# - Build dynamic queries using MongoDB query operators.
# - Implement pagination with proper metadata.
# - Enable sorting for allowed fields with proper indexing.
# - Include computed analytics using MongoDB aggregation pipeline.
# - Use NestJS decorators for controllers, services, and modules.
# - Document API with Swagger using NestJS decorators.
# - Sanitize and validate all query params to prevent NoSQL injection.
# - Add indexes on frequently queried/sorted fields.
# - Return structured response with data, pagination, and analytics.
# - Use ConfigService for environment-based configuration.
# """
# }

# List API
LIST_TEMPLATE_SPECIFIC = {
    "typescript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Validate inputs at runtime with `zod` or `class-validator`.
- Expose clean endpoints like `GET /resources` and `GET /analytics/years-of-service`.
- Support dynamic filtering (exact, substring, enum, and date-based). Centralize filtering logic.
- Implement `limit` and `skip` with defaults (`10` and `0`), support total count in response.
- Allow sorting with `sortBy` and `order` (default: `asc`), validate allowed fields.
- Use SQL aggregations (`COUNT`, `SUM`, etc.) for analytical endpoints.
- Sanitize and validate inputs to avoid SQL injection.
- Wrap write operations in DB transactions.
- Log slow queries and capture detailed error logs.
- Load sensitive configs like DB URIs via `.env` using `dotenv`.
- Return structured responses: `{ success, data, error, pagination }`.
- Apply rate limiting with `express-rate-limit`.
- Example Query: /resources?limit=20&skip=0&sortBy=name&order=asc&filters[name]=widget&filters[status]=active
""",
    "typescript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Use `zod` or `class-validator` for runtime validation of inputs and query params.
- Support dynamic filters via query strings and centralize filter logic (e.g., `buildWhereClause()`). Validate filter fields/types.
- Implement pagination with `limit` and `offset`; cap `limit`.
- Enable `sortBy` and `order` (`asc`/`desc`); only allow whitelisted, indexed fields.
- Expose `/analytics/...` endpoints using SQL aggregations (`COUNT`, `AVG`, etc.).
- Sanitize and validate all inputs to prevent SQL injection.
- Use transactions for write operations.
- Add indexes on frequently queried/sorted fields; use composite indexes.
- Monitor query performance; log slow queries and errors.
- Rate-limit sensitive routes using `express-rate-limit`.
- Load environment configs with `dotenv` for DB URI, caps, logging, rate limits.
- Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
- Sample Endpoint Examples: GET /resources?limit=10&offset=0&sortBy=createdAt&order=desc, GET /analytics/summary
""",
    "typescript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Validate inputs using `zod`, `class-validator`, or Mongoose schema validators.
- Accept dynamic filters via query strings using regex, `$in`, `$gte`, etc. Centralize filtering logic; validate filters.
- Support `limit` and `skip` with sane defaults and caps; use cursor pagination optionally.
- Allow sorting via `sortBy` and `order` with validation for indexed fields.
- Expose `/analytics/...` endpoints using aggregation pipelines (`$group`, `$expr`, etc.). Optimize analytics.
- Sanitize and validate all query params to prevent NoSQL injection.
- Use MongoDB transactions with session-based logic.
- Add indexes on frequently queried/sorted fields; use compound/partial/TTL indexes.
- Monitor query performance using MongoDB profiler; log slow queries and errors.
- Rate-limit sensitive routes using `express-rate-limit`.
- Load environment configs with `dotenv` for DB URI, caps, logging, rate limits.
- Standardize API responses with `success`, `data`, `pagination`, and `error` fields.
- Example Generic Endpoints: GET /resources?limit=10&skip=0&sortBy=createdAt&order=desc, GET /analytics/grouped-by-status
""",
    "python_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Use Pydantic v2 for strict typing and validation of query params.
- Support dynamic filters via query strings; centralize filter logic in a helper function.
- Implement pagination with `limit` and `skip` query params; cap `limit` at a reasonable value.
- Allow sorting via `sort_by` and `order` with validation for allowed fields.
- Expose `/analytics/...` endpoints using SQL aggregations.
- Sanitize and validate all inputs to prevent SQL injection.
- Use SQLAlchemy transactions for write operations.
- Add indexes on frequently queried/sorted fields.
- Monitor query performance; log slow queries and errors.
- Optionally use middleware (e.g., `slowapi`) to limit high-traffic endpoints.
- Load environment variables (`DATABASE_URL`, `MAX_PAGE_LIMIT`) from `.env`.
- Example Queries: /resources?name=widget&limit=20&skip=10&sort_by=name&order=asc, /items?created_at_from=2022-01-01&created_at_to=2023-01-01
""",
    "python_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Use `asyncpg` with a shared pool.
- Enable `pg_trgm` for fuzzy/partial matching.
- Support dynamic filters: IDs, categories, dates, etc.; text fields use `ILIKE`, date ranges via `from_date`, `to_date`.
- Apply pagination with `page` and `limit` query params using `.offset().limit()`.
- Allow sorting by indexed fields via `sort_by` and `order`; validate inputs.
- Use Pydantic v2 for strict typing, field validation, and sanitization; return 422 for invalid sort/filter.
- Parameterize all queries to prevent SQL injection. Sanitize inputs.
- Standard response: `success`, `data`, `error`, and `pagination` metadata.
- Log slow queries with `EXPLAIN ANALYZE`.
- Use `async with session.begin()` for transactions.
- Load configs (DB URI, timeouts, etc.) from `.env`.
- Optional `/analytics/dimension` routes for grouped metrics using SQL aggregations.
- Use `slowapi` to limit access to high-traffic endpoints.
""",
    "python_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Motor/Beanie for async MongoDB access.
- Define generic resource schema using Pydantic v2 or Motor schema.
- Support dynamic filters for fields like `id`, `category`, `status`, and text/date filters. Reject fuzzy regex unless allowed.
- Implement pagination with `limit` (default: 10) and `skip`.
- Support sorting with whitelisted indexed fields (`sort_by`) and order (`asc`/`desc`). Validate sorting inputs.
- Use Pydantic v2 for query and response validation, raising HTTP 422 for invalid input.
- Sanitize inputs and parameterize MongoDB queries to prevent injection.
- Use a standard response structure with `success`, `data`, `pagination`, and `error`.
- Log slow queries and set up MongoDB profiler. Use compound indexes selectively.
- Load configurations (`MONGO_URI`, limits, etc.) from `.env` using `pydantic-settings`.
- Optionally add `/analytics/dimension` routes for aggregations.
- Use `slowapi` for rate limiting.
- Examples: /resources?category=tools&status=active&limit=10&sort_by=name&order=asc, /products?type=software&skip=20&limit=10
""",
    # JavaScript entries
    "javascript_mysql": """
REQUIREMENTS_SPECIFIC:
- Include MySQL DDL script.
- Validate inputs with `joi` or similar validation libraries.
- Support dynamic filtering (exact, substring, enum, date-based) with centralized filter logic.
- Implement pagination with `limit` and `offset` parameters and default values.
- Enable sorting with `sortBy` and `order` parameters for allowed fields.
- Create analytics endpoints using SQL aggregations for metrics and summaries.
- Use parameterized queries to prevent SQL injection.
- Add indexes on frequently queried and sorted fields.
- Implement error handling with appropriate status codes and messages.
- Log slow queries and errors with context information.
- Use environment variables for configuration with `dotenv`.
- Return consistent response format with success, data, and pagination info.
- Example: GET /resources?limit=20&offset=0&sortBy=name&order=asc&filters[name]=widget
""",
    "javascript_postgresql": """
REQUIREMENTS_SPECIFIC:
- Include PostgreSQL DDL script.
- Use `joi` for validation of query parameters and inputs.
- Support dynamic filters with centralized query building logic.
- Implement pagination with `limit` and `offset` parameters.
- Allow sorting with `sortBy` and `order` for whitelisted fields.
- Create analytics endpoints using PostgreSQL aggregation functions.
- Use Sequelize prepared statements to prevent SQL injection.
- Add appropriate indexes for frequently queried fields.
- Implement proper error handling with status codes.
- Monitor query performance and log slow queries.
- Load configuration from environment variables with `dotenv`.
- Return standardized response format with pagination metadata.
- Example: GET /resources?limit=10&offset=0&sortBy=createdAt&order=desc
""",
    "javascript_mongodb": """
REQUIREMENTS_SPECIFIC:
- Use Mongoose for MongoDB schema definition and validation.
- Support dynamic filters using MongoDB query operators.
- Implement pagination with `limit` and `skip` parameters.
- Enable sorting with `sortBy` and `order` parameters.
- Create analytics endpoints using MongoDB aggregation pipeline.
- Validate and sanitize inputs to prevent NoSQL injection.
- Add indexes on frequently queried and sorted fields.
- Implement proper error handling with appropriate status codes.
- Monitor query performance and log slow operations.
- Use environment variables for configuration with `dotenv`.
- Return consistent response format with success, data, and pagination.
- Example: GET /resources?limit=10&skip=0&sortBy=createdAt&order=desc
""",
    # Next.js entries
    "nextjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Define MySQL schema using Prisma with appropriate relations and indexes.
- Create API routes for listing resources with dynamic filtering.
- Implement pagination with `take` and `skip` parameters in Prisma queries.
- Support sorting with `orderBy` parameter for allowed fields.
- Build analytics components using SQL aggregations via Prisma.
- Use React Server Components for data fetching where appropriate.
- Implement client-side filtering and sorting with SWR or React Query.
- Add loading states and error handling in the UI.
- Use parameterized queries through Prisma to prevent SQL injection.
- Validate inputs with zod or similar libraries.
- Configure environment variables for database connection.
- Return consistent API response format with pagination metadata.
""",
    "nextjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Define PostgreSQL schema using Prisma with appropriate relations and indexes.
- Create API routes for listing resources with dynamic filtering.
- Implement pagination with `take` and `skip` parameters in Prisma queries.
- Support sorting with `orderBy` parameter for allowed fields.
- Build analytics components using PostgreSQL-specific features via Prisma.
- Use React Server Components for data fetching where appropriate.
- Implement client-side filtering and sorting with SWR or React Query.
- Add loading states and error handling in the UI.
- Use parameterized queries through Prisma to prevent SQL injection.
- Validate inputs with zod or similar libraries.
- Configure environment variables for database connection.
- Return consistent API response format with pagination metadata.
""",
    "nextjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Define Mongoose schemas with appropriate validation and indexes.
- Create API routes for listing resources with dynamic filtering.
- Implement pagination with `limit` and `skip` parameters.
- Support sorting with `sort` parameter for allowed fields.
- Build analytics components using MongoDB aggregation pipeline.
- Use React Server Components for data fetching where appropriate.
- Implement client-side filtering and sorting with SWR or React Query.
- Add loading states and error handling in the UI.
- Validate and sanitize inputs to prevent NoSQL injection.
- Add proper error handling with appropriate status codes.
- Configure environment variables for database connection.
- Return consistent API response format with pagination metadata.
""",
    # NestJS entries
    "nestjs_mysql": """
REQUIREMENTS_SPECIFIC:
- Define MySQL entities using TypeORM with appropriate relations and indexes.
- Create DTOs for query parameters with class-validator decorations.
- Implement pagination using TypeORM's `take` and `skip` options.
- Support sorting with query parameters validated against allowed fields.
- Build analytics endpoints using TypeORM's query builder for aggregations.
- Use NestJS interceptors for consistent response formatting.
- Implement proper exception filters for error handling.
- Document API with Swagger using NestJS decorators.
- Use TypeORM's parameterized queries to prevent SQL injection.
- Configure environment variables with NestJS ConfigService.
- Implement rate limiting with NestJS ThrottlerModule.
- Return consistent response format with pagination metadata.
""",
    "nestjs_postgresql": """
REQUIREMENTS_SPECIFIC:
- Define PostgreSQL entities using TypeORM with appropriate relations and indexes.
- Create DTOs for query parameters with class-validator decorations.
- Implement pagination using TypeORM's `take` and `skip` options.
- Support sorting with query parameters validated against allowed fields.
- Build analytics endpoints using PostgreSQL-specific features via TypeORM.
- Use NestJS interceptors for consistent response formatting.
- Implement proper exception filters for error handling.
- Document API with Swagger using NestJS decorators.
- Use TypeORM's parameterized queries to prevent SQL injection.
- Configure environment variables with NestJS ConfigService.
- Implement rate limiting with NestJS ThrottlerModule.
- Return consistent response format with pagination metadata.
""",
    "nestjs_mongodb": """
REQUIREMENTS_SPECIFIC:
- Define MongoDB schemas using NestJS Mongoose module.
- Create DTOs for query parameters with class-validator decorations.
- Implement pagination using Mongoose's `limit` and `skip` methods.
- Support sorting with query parameters validated against allowed fields.
- Build analytics endpoints using MongoDB aggregation pipeline.
- Use NestJS interceptors for consistent response formatting.
- Implement proper exception filters for error handling.
- Document API with Swagger using NestJS decorators.
- Validate and sanitize inputs to prevent NoSQL injection.
- Configure environment variables with NestJS ConfigService.
- Implement rate limiting with NestJS ThrottlerModule.
- Return consistent response format with pagination metadata.
"""
}

# --- Template Dictionary Mapping ---

TEMPLATE_TYPE_MAP = {
    "TEMPLATES": TEMPLATES_SPECIFIC,
    "DELETE": DELETE_TEMPLATE_SPECIFIC,
    "FETCH": FETCH_TEMPLATE_SPECIFIC,
    "SEARCH": SEARCH_TEMPLATE_SPECIFIC,
    "LIST": LIST_TEMPLATE_SPECIFIC,
}

# --- Helper Functions ---

LANGUAGES = {
    "python": ["python", "fastapi", "flask", "django", "pytorch", "tensorflow"],
    "javascript": [
        "javascript",
        "js",
        "node",
        "express",
        "react",
        "vue",
        "angular",
    ],
    "typescript": [
        "typescript",
        "ts",
        "node.js with typescript",
        "angular",
        "next.js",
    ],
       "nextjs": [
        "next.js",
        "react",
    ],
     "nestjs": [
        "nest.js",
    ],
    "java": ["java", "spring", "springboot", "spring boot"],
    "csharp": ["c#", "csharp", ".net", "asp.net", "dotnet"],
    "go": ["go", "golang"],
    "rust": ["rust", "cargo"],
    "php": ["php", "laravel", "symfony"],
    "ruby": ["ruby", "rails", "ruby on rails"],
    "swift": ["swift", "ios"],
    "kotlin": ["kotlin", "android"],
    "dart": ["dart", "flutter"],
}

DATABASES = {
    "mysql": ["mysql", "mariadb"],
    "postgresql": ["postgresql", "postgres", "psql", "postgre"],
    "mongodb": ["mongodb", "mongo", "nosql"],
    "sqlite": ["sqlite", "sqlite3"],
    "oracle": ["oracle", "oracledb"],
    "mssql": ["mssql", "sql server", "sqlserver", "microsoft sql"],
    "dynamodb": ["dynamodb", "dynamo", "aws dynamo"],
    "cassandra": ["cassandra"],
    "redis": ["redis"],
    "firestore": ["firestore", "firebase"],
    "neo4j": ["neo4j", "neo4j graph"],
}


def get_template(
    template_type: str, language: str, database: str, default_db: str = "mysql"
) -> str:
    """
    Generic function to get the appropriate template based on language, database, and template type.

    Args:
        template_type: The type of template (e.g., "DELETE", "FETCH", "TEMPLATES").
        language: The programming language (e.g., "python", "typescript").
        database: The database type (e.g., "mysql", "mongodb").
        default_db: Default database to use if specific combination not found.

    Returns:
        String template with combined common and specific parts.
    """
    lookup_key = f"{language}_{database}"
    specific_template_dict = TEMPLATE_TYPE_MAP.get(template_type, TEMPLATES_SPECIFIC)

    # Get DB specific details
    db_detail_key = lookup_key if lookup_key in DB_DETAILS else f"{language}_{default_db}"
    db_info = DB_DETAILS.get(db_detail_key, DB_DETAILS[default_db]) # Fallback to default mysql if lang default not found

    # Get common structure
    if language == "python":
        common_structure = PYTHON_BASE_STRUCTURE
        common_requirements = COMMON_PYTHON_REQUIREMENTS
        doc_requirement = COMMON_PYTHON_DOCSTRING_REQUIREMENT
    elif language == "typescript":
        common_structure = TS_BASE_STRUCTURE
        common_requirements = COMMON_TS_REQUIREMENTS
        doc_requirement = COMMON_JSDOC_REQUIREMENT
    elif language == "javascript":
        common_structure = JS_BASE_STRUCTURE
        common_requirements = COMMON_JS_REQUIREMENTS
        doc_requirement = COMMON_JSDOC_REQUIREMENT
    elif language == "nextjs":
        common_structure = NEXTJS_BASE_STRUCTURE
        common_requirements = COMMON_NEXTJS_REQUIREMENTS
        doc_requirement = COMMON_JSDOC_REQUIREMENT
    elif language == "nestjs":
        common_structure = NESTJS_BASE_STRUCTURE
        common_requirements = COMMON_NESTJS_REQUIREMENTS
        doc_requirement = COMMON_JSDOC_REQUIREMENT
    else:
        # Fallback for other languages - might need expansion
        common_structure = f"IMPORTANT: Project structure for {language} with {database}..."
        common_requirements = f"REQUIREMENTS:\n1. Basic setup for {language} and {database}..."
        doc_requirement = ""

    # Format structure with DB specifics
    ddl_section = """
├── ddl/
│   └── [resource].sql        # {db_type} DDL script""".format(db_type=db_info['orm']) if db_info['ddl'] else ""

    formatted_structure = common_structure.format(
        db_type=database.capitalize(),
        db_orm=db_info['orm'],
        dao_or_repo=db_info['dao_or_repo'],
        dao_or_repo_short=db_info['dao_or_repo_short'],
        dao_or_repo_name=db_info['dao_or_repo_name'],
        ddl_structure=ddl_section
    )

    # Get specific requirements
    specific_requirements = specific_template_dict.get(lookup_key)
    if not specific_requirements:
        # Fallback to default DB for the language if specific combo not found
        fallback_key = f"{language}_{default_db}"
        specific_requirements = specific_template_dict.get(fallback_key, f"REQUIREMENTS_SPECIFIC:\n- Basic setup for {database}.\n")
        # Replace default DB name in fallback if needed (simple replacement)
        specific_requirements = specific_requirements.replace(default_db.capitalize(), database.capitalize())
        specific_requirements = specific_requirements.replace(DB_DETAILS[fallback_key]['orm'], db_info['orm'])


    # Combine all parts
    final_template = (
        BASE_PROMPT
        + "\nIMPORTANT: Follow this exact project structure:\n"
        + formatted_structure
        + "\n"
        + doc_requirement # Add JSDoc/Docstring requirement
        + "\nREQUIREMENTS_COMMON:\n"
        + common_requirements
        + "\n"
        + specific_requirements # This already contains "REQUIREMENTS_SPECIFIC:"
    )

    return final_template


# --- Public Functions ---

def get_database_template(language: str, database: str) -> str:
    """Get the template for general database/API setup."""
    return get_template("TEMPLATES", language, database)

def get_delete_template(language: str, database: str) -> str:
    """Get the template specifically for delete operations."""
    # Note: We're not using get_template() here because we want to use DELETE_BASE_PROMPT directly
    # This ensures we only generate delete functionality, not full CRUD
    return DELETE_BASE_PROMPT

def get_fetch_template(language: str, database: str) -> str:
    """Get the template specifically for fetch/get operations."""
    # Note: We're not using get_template() here because we want to use FETCH_BASE_PROMPT directly
    # This ensures we only generate fetch functionality, not full CRUD
    return FETCH_BASE_PROMPT

def get_search_database_template(language: str, database: str) -> str:
    """Get the template specifically for search operations."""
    # Note: We're not using get_template() here because we want to use SEARCH_BASE_PROMPT directly
    # This ensures we only generate search functionality, not full CRUD
    return SEARCH_BASE_PROMPT

def get_list_database_template(language: str, database: str) -> str:
    """Get the template specifically for list operations."""
    # Note: We're not using get_template() here because we want to use LIST_BASE_PROMPT directly
    # This ensures we only generate list functionality, not full CRUD
    return LIST_BASE_PROMPT
