BASE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt, generate complete API code in {language} using {database} as the database:
        Prompt: {prompt}

Your response MUST include comprehensive test cases for all API endpoints and database operations.
Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks.
Do not add any numbers or bullets before the file paths.

Be sure to include appropriate test files with comprehensive test cases covering:
1. Error handling tests for invalid inputs
2. Edge case tests
3. Mocking of database connections using Jest for testing with appropriate mocks with MockedFunction & mockResolvedValue objects.
"""

UPDATE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt requesting UPDATE functionality for a resource, generate a COMPLETE, RUNNABLE application in {language} using {database} as the database, focusing *only* on the necessary components for the UPDATE operation.

Prompt: {prompt} # Example: "Generate a node ts code for updating employee name, address, phone number"

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks. Do not add any numbers or bullets before the file paths.

You must create ALL files needed for a complete, runnable application, adhering to the specified structure. Functional code within controllers, services, DAOs, routes, and schemas should primarily support the UPDATE operation requested.

IMPORTANT: Generate a COMPLETE TypeScript + Express + MySQL application with this exact structure:

src/
├── config/
│   └── database.ts             # Sequelize + MySQL connection setup (use env vars)
├── secrets/                    # Optional: Placeholder for external secrets integration
│   └── secrets.manager.ts
├── logger/
│   └── logger.ts               # Winston JSON logger configuration (from env vars)
├── ddl/
│   └── [resource].sql          # MySQL DDL: table creation, indexes, constraints, timestamps, deletedAt?
├── controllers/
│   └── [resource].controller.ts # Implements ONLY the UPDATE method (calls service, handles req/res, uses next(error)).
├── dao/
│   └── [resource].dao.ts       # Implements ONLY update (returns affected rows) and findById methods. Uses transactions.
├── models/
│   └── [resource].model.ts     # Full Sequelize model definition (timestamps: true, paranoid?: true). Includes interfaces with timestamps.
├── routes/
│   └── [resource].routes.ts    # Defines ONLY the UPDATE route (PUT/PATCH /:id), applies validation & auth middleware, connects to controller.
├── services/
│   └── [resource].service.ts   # Implements ONLY UPDATE business logic (calls dao.update -> checks rows -> calls dao.findById -> returns entity). Throws NotFoundError.
├── schemas/
│   └── [resource].schema.ts    # Defines IResource, UpdateResourceDto (optional fields), and its validation schema (Joi/class-validator).
├── middleware/
│   ├── validation.middleware.ts # Generic schema validation middleware runner.
│   ├── errorHandler.ts         # Global error handling middleware.
│   └── authMiddleware.ts       # Placeholder auth middleware.
├── errors/
│   └── httpErrors.ts           # Custom HTTP error classes (BadRequestError, NotFoundError, etc.).
├── app.ts                      # Express app setup: Registers middleware (JSON, CORS, logger, routes, error handler).
├── .env                        # Environment variables (DB_HOST, DB_USER, DB_PASS, DB_NAME, PORT, LOG_LEVEL, NODE_ENV).
├── package.json                # Node.js dependencies & basic scripts (start, build, dev).
├── tsconfig.json               # TypeScript configuration (strict: true).
└── server.ts                   # Entry point: Loads env, logger, connects DB (async), starts server.

CRITICAL REQUIREMENTS FOR HIGH-QUALITY UPDATE API:

1. Focus EXCLUSIVELY on the UPDATE operation for the specific fields mentioned in the prompt
2. Implement a PATCH endpoint (/:id) that allows partial updates of only the specified fields
3. Validate that at least one field is being updated (don't allow empty update objects)
4. Implement proper field validation (e.g., string length, format validation for emails/phones)
5. Return the complete updated resource after successful update
6. Use HTTP 200 for successful updates with the updated resource in the response body
7. Use HTTP 404 if the resource doesn't exist
8. Use HTTP 400 for validation errors with clear error messages
9. Include proper error handling for database errors
10. Implement transaction support for the update operation
11. Add appropriate logging for the update operation
12. Generate comprehensive test cases for the update functionality
13. Generate complete Swagger (OpenAPI 3.0) documentation for update api endpoints, with schema definitions, query parameters, response types, and examples. Ensure the generated documentation is compatible with Swagger UI or Redoc.

Database Implementation:
- Define a robust Sequelize model with appropriate types and validations for all fields
- Implement an update method that returns affected row count and uses transactions
- Implement a findById method to retrieve the updated entity after update
- Handle database errors properly and convert to appropriate HTTP errors

API Flow:
- Routes: Define only the PATCH route with validation middleware
- Controller: Parse ID, validate request body, call service, return updated resource
- Service: Call DAO update, check affected rows (throw NotFoundError if 0), return updated entity
- Validation: Define UpdateDto with optional fields and validation rules
- Error Handling: Implement custom error classes and global error handler

Be thorough in generating the complete application structure, but focus the implemented logic strictly on what's needed for the requested UPDATE functionality. The code should be production-ready, secure, and follow best practices for TypeScript and Express.
"""


SEARCH_BASE_PROMPT = """
You are an expert API designer and backend developer. Based on this prompt, generate SEARCH API code in {language} using {database} as the database. Focus STRICTLY on search functionality — do NOT implement create, update, or delete operations in any form.

Prompt: {prompt}  # Example: "Create a search API for employees that allows filtering by name, department, and hire date."

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext

```{{language}}
// Code content here
```

Each file path must start with "# File: " followed by the exact relative path, and code must be enclosed in triple backticks. Do not add any numbers, bullets, or commentary before or after file paths or code blocks.

IMPORTANT: Generate a COMPLETE application with this exact structure, but ONLY implement the SEARCH functionality:

src/
├── config/
│   └── database.ts               # Database connection setup (use env vars)
├── logger/
│   └── logger.ts                 # Logger configuration (from env vars)
├── ddl/
│   └── [resource].sql            # DDL: table creation, indexes, constraints
├── controllers/
│   └── [resource].controller.ts  # Implements ONLY search method (no create, update, delete)
├── dao/
│   └── [resource].dao.ts         # Implements ONLY search method (no create, update, delete)
├── models/
│   └── [resource].model.ts       # Model definition
├── routes/
│   └── [resource].routes.ts      # Defines ONLY search route(s) based on the user's prompt
├── services/
│   └── [resource].service.ts     # Implements ONLY search business logic
├── schemas/
│   └── [resource].schema.ts      # Defines SearchParams interface and validation schema
├── middleware/
│   ├── validation.middleware.ts  # Schema validation middleware (e.g., using zod or joi)
│   ├── errorHandler.ts           # Error handling middleware
│   └── authMiddleware.ts         # Auth middleware (stub or basic example)
├── errors/
│   └── httpErrors.ts             # Custom error classes
├── app.ts                        # Express app setup
├── .env                          # Environment variables
├── package.json                  # Dependencies & scripts
├── tsconfig.json                 # TypeScript configuration
└── server.ts                     # Entry point

STRICT REQUIREMENTS FOR SEARCH API:

CRUD Scope Limitation:
- Implement ONLY search functionality, no create, update, or delete operations.
- Focus on efficient, flexible search with proper filtering, sorting, and pagination.
- Ensure all search parameters are properly validated and sanitized.

- **Filtering**: Support filters only on fields marked filterable in the schema. Map filters to parameterized SQL queries. For example: `name LIKE '%value%'` for partial match, `department = 'HR'` for exact match, `created_at >= '2023-01-01'` for date range.
- **Sorting**: Allow sorting only by fields marked sortable. Example: `ORDER BY created_at DESC`. Reject sorting on non-sortable fields.
- **Pagination**: Use `limit` and `page` (or `offset`). Default `limit = 10`, maximum `limit = 100`. Compute `offset = (page - 1) * limit`. Return `page`, `limit`, and `total` in responses.
- **Schema Enforcement**: Enforce `filterable`, `sortable`, and `analyticsFields` based on schema metadata. Ignore or reject filters/sorts on non-permitted fields (400). Only fields declared for analytics can be used in aggregate queries.
- **Validation & Error Handling**: Use a validation library (e.g., class-validator or Joi) to validate query parameters. Return HTTP 400 for invalid inputs with descriptive errors; use HTTP 500 for server errors.
- **Indexing**: Create indexes based on query patterns. Avoid redundant single-column indexes; prefer composite indexes for multi-field queries (e.g., `CREATE INDEX idx_status_date ON table(status, created_at)`). Ensure indexes on columns frequently used in filters or joins.
- **Response Format**: Return JSON:
  - ``data``: array of record objects.
  - ``pagination``: object with ``total``, ``limit``, ``page``.
  - ``analytics``: object with any aggregate info (if applicable).
  - ``errors``: list of error details (if any).
Example: ``{{"data":[...],"pagination":{{"total":50,"limit":10,"page":1}},"analytics":{{}}}}``

Follow the prompt strictly and generate only what is necessary to fulfill the search API request with the architecture above.
"""

LIST_BASE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt, generate LIST API code in {language} using {database} as the database. Focus ONLY on list functionality, not on other CRUD operations.

Prompt: {prompt} # Example: "Create a list API for employees with sorting by name, department and date of joining"

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks.
Do not add any numbers or bullets before the file paths.

IMPORTANT: Generate a COMPLETE application with this exact structure, but ONLY implement the LIST functionality:

src/
├── config/
│   └── database.ts             # Database connection setup (use env vars)
├── logger/
│   └── logger.ts               # Logger configuration (from env vars)
├── ddl/
│   └── [resource].sql          # DDL: table creation, indexes, constraints
├── controllers/
│   └── [resource].controller.ts # Implements ONLY list method (no create, update, delete)
├── dao/
│   └── [resource].dao.ts       # Implements ONLY list method (no create, update, delete)
├── models/
│   └── [resource].model.ts     # Model definition
├── routes/
│   └── [resource].routes.ts    # Defines ONLY list route (GET /), applies validation middleware
├── services/
│   └── [resource].service.ts   # Implements ONLY list business logic
├── schemas/
│   └── [resource].schema.ts    # Defines ListParams interface and validation schema
├── middleware/
│   ├── validation.middleware.ts # Schema validation middleware
│   ├── errorHandler.ts         # Error handling middleware
│   └── authMiddleware.ts       # Auth middleware
├── errors/
│   └── httpErrors.ts           # Custom error classes
├── app.ts                      # Express app setup
├── .env                        # Environment variables
├── package.json                # Dependencies & scripts
├── tsconfig.json               # TypeScript configuration
└── server.ts                   # Entry point

CRITICAL REQUIREMENTS FOR LIST API:
- Focus ONLY on list functionality - do NOT implement create, update, or delete operations
- Implement a GET / endpoint with query parameters for filtering
- Support basic filtering (exact match, enum values)
- Implement pagination using limit and offset parameters
- Support sorting via sortBy and order parameters
- Return list results with metadata (total count, pagination info)
- Include comprehensive test cases for the list functionality
- Validate and sanitize inputs to prevent injection attacks
- Add appropriate indexes for list performance
- Generate complete Swagger (OpenAPI 3.0) documentation for all list api endpoints, with schema definitions, query parameters, response types, and examples. Ensure the generated documentation is compatible with Swagger UI or Redoc.

Be thorough in generating the complete application structure, but focus the implemented logic strictly on what's needed for the requested LIST functionality.
"""

FETCH_BASE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt, generate FETCH API code in {language} using {database} as the database. Focus ONLY on fetch functionality, not on other CRUD operations.

Prompt: {prompt} # Example: "Create a fetch API for the employee resource in typescript and mysql that retrieves details of a single employee by ID."

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks.
Do not add any numbers or bullets before the file paths.

IMPORTANT: Generate a COMPLETE application with this exact structure, but ONLY implement the FETCH functionality:

src/
├── config/
│   └── database.ts             # Database connection setup (use env vars)
├── logger/
│   └── logger.ts               # Logger configuration (from env vars)
├── ddl/
│   └── [resource].sql          # DDL: table creation, indexes, constraints
├── controllers/
│   └── [resource].controller.ts # Implements ONLY fetch method (no create, update, delete)
├── dao/
│   └── [resource].dao.ts       # Implements ONLY fetch method (no create, update, delete)
├── models/
│   └── [resource].model.ts     # Model definition
├── routes/
│   └── [resource].routes.ts    # Defines ONLY fetch route (GET /:id), applies validation middleware
├── services/
│   └── [resource].service.ts   # Implements ONLY fetch business logic
├── schemas/
│   └── [resource].schema.ts    # Defines resource interface and validation schema
├── middleware/
│   ├── validation.middleware.ts # Schema validation middleware
│   ├── errorHandler.ts         # Error handling middleware
│   └── authMiddleware.ts       # Auth middleware
├── errors/
│   └── httpErrors.ts           # Custom error classes
├── app.ts                      # Express app setup
├── .env                        # Environment variables
├── package.json                # Dependencies & scripts
├── tsconfig.json               # TypeScript configuration
└── server.ts                   # Entry point

CRITICAL REQUIREMENTS FOR FETCH API:

- Focus ONLY on fetch functionality - do NOT implement create, update, or delete operations
- Implement a GET /:id endpoint to retrieve a single resource by ID
- Handle not found errors properly (404 status code)
- Include comprehensive test cases for the fetch functionality
- Validate and sanitize inputs to prevent injection attacks
- Add appropriate indexes for fetch performance
- Generate complete Swagger (OpenAPI 3.0) documentation for all fetch api endpoints, with schema definitions, query parameters, response types, and examples. Ensure the generated documentation is compatible with Swagger UI or Redoc.

Be thorough in generating the complete application structure, but focus the implemented logic strictly on what's needed for the requested FETCH functionality.
"""

DELETE_BASE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt, generate DELETE API code in {language} using {database} as the database. Focus ONLY on delete functionality, not on other CRUD operations.

Prompt: {prompt} # Example: "Create a delete API for the employee resource in typescript and mysql that soft deletes an employee by ID."

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks.
Do not add any numbers or bullets before the file paths.

IMPORTANT: Generate a COMPLETE application with this exact structure, but ONLY implement the DELETE functionality:

src/
├── config/
│   └── database.ts             # Database connection setup (use env vars)
├── logger/
│   └── logger.ts               # Logger configuration (from env vars)
├── ddl/
│   └── [resource].sql          # DDL: table creation, indexes, constraints
├── controllers/
│   └── [resource].controller.ts # Implements ONLY delete method (no create, update, fetch)
├── dao/
│   └── [resource].dao.ts       # Implements ONLY delete method (no create, update, fetch)
├── models/
│   └── [resource].model.ts     # Model definition
├── routes/
│   └── [resource].routes.ts    # Defines ONLY delete route (DELETE /:id), applies validation middleware
├── services/
│   └── [resource].service.ts   # Implements ONLY delete business logic
├── schemas/
│   └── [resource].schema.ts    # Defines resource interface and validation schema
├── middleware/
│   ├── validation.middleware.ts # Schema validation middleware
│   ├── errorHandler.ts         # Error handling middleware
│   └── authMiddleware.ts       # Auth middleware
├── errors/
│   └── httpErrors.ts           # Custom error classes
├── app.ts                      # Express app setup
├── .env                        # Environment variables
├── package.json                # Dependencies & scripts
├── tsconfig.json               # TypeScript configuration
└── server.ts                   # Entry point

CRITICAL REQUIREMENTS FOR DELETE API:

- Focus ONLY on delete functionality - do NOT implement create, update, or fetch operations
- Implement a DELETE /:id endpoint to delete a resource by ID
- Implement soft delete (set deletedAt timestamp) rather than hard delete where appropriate
- Handle not found errors properly (404 status code)
- Include comprehensive test cases for the delete functionality
- Validate and sanitize inputs to prevent injection attacks
- Generate complete Swagger (OpenAPI 3.0) documentation for all delete api endpoints, with schema definitions, query parameters, response types, and examples. Ensure the generated documentation is compatible with Swagger UI or Redoc.

Be thorough in generating the complete application structure, but focus the implemented logic strictly on what's needed for the requested DELETE functionality.
"""

OVERWRITE_PROMPT = """
You are an expert API designer and Backend developer. Based on this prompt, generate a COMPLETE, RUNNABLE application with full CRUD functionality in {language} using {database} as the database, with special focus on the OVERWRITE operation.

Prompt: {prompt} # Example: "Generate a node ts code for overwriting employee data including name, address, phone number"

Your response MUST follow this exact format for file paths and code blocks:

# File: path/to/filename.ext
```{language}
// Code content here
```

Each file path must start with "# File: " followed by the exact path, and code must be enclosed in triple backticks. Do not add any numbers or bullets before the file paths.

You must create ALL files needed for a complete, runnable application, adhering to the specified structure and requirements below.

IMPORTANT: Generate a COMPLETE TypeScript + Express + MySQL application with this exact structure:

src/
├── config/
│   └── database.ts             # Sequelize + MySQL connection setup (use env vars)
├── secrets/                    # Optional: Placeholder for external secrets integration
│   └── secrets.manager.ts
├── logger/
│   └── logger.ts               # Winston JSON logger configuration (from env vars)
├── ddl/
│   └── [resource].sql          # MySQL DDL: table creation, indexes, constraints, timestamps, deletedAt?
├── controllers/
│   └── [resource].controller.ts # Implements ALL CRUD methods with special focus on PUT for complete resource overwrite
├── dao/
│   └── [resource].dao.ts       # Implements ALL CRUD methods with special focus on overwrite method
├── models/
│   └── [resource].model.ts     # Full Sequelize model definition (timestamps: true, paranoid?: true)
├── routes/
│   └── [resource].routes.ts    # Defines ALL CRUD routes with special focus on PUT /:id for complete overwrite
├── services/
│   └── [resource].service.ts   # Implements business logic for ALL CRUD operations with special focus on overwrite
├── schemas/
│   └── [resource].schema.ts    # Defines IResource, CreateResourceDto, OverwriteResourceDto, and validation schemas
├── middleware/
│   ├── validation.middleware.ts # Generic schema validation middleware runner
│   ├── errorHandler.ts         # Global error handling middleware
│   └── authMiddleware.ts       # Placeholder auth middleware
├── errors/
│   └── httpErrors.ts           # Custom HTTP error classes (BadRequestError, NotFoundError, etc.)
├── app.ts                      # Express app setup: Registers middleware (JSON, CORS, logger, routes, error handler)
├── .env                        # Environment variables (DB_HOST, DB_USER, DB_PASS, DB_NAME, PORT, LOG_LEVEL, NODE_ENV)
├── package.json                # Node.js dependencies & basic scripts (start, build, dev)
├── tsconfig.json               # TypeScript configuration (strict: true)
└── server.ts                   # Entry point: Loads env, logger, connects DB (async), starts server

CRITICAL REQUIREMENTS FOR HIGH-QUALITY OVERWRITE API:

1. Implement a complete RESTful API with special focus on the OVERWRITE operation
2. The OVERWRITE operation must completely replace an existing resource with new data
3. Use PUT method for the overwrite endpoint (/:id) that requires ALL fields to be provided
4. Implement strict validation for the overwrite operation (all required fields must be present)
5. Return the completely overwritten resource after successful operation
6. Use HTTP 200 for successful overwrites with the updated resource in the response body
7. Use HTTP 404 if the resource doesn't exist
8. Use HTTP 400 for validation errors with clear error messages
9. Include proper error handling for database errors
10. Implement transaction support for the overwrite operation
11. Add appropriate logging for all operations
12. Generate complete Swagger (OpenAPI 3.0) documentation for all overwrite endpoints, with schema definitions, query parameters, response types, and examples. Ensure the generated documentation is compatible with Swagger UI or Redoc.
13. Generate comprehensive test cases for all functionality

Key Differences Between UPDATE and OVERWRITE:
- UPDATE (PATCH): Allows partial updates with only specified fields
- OVERWRITE (PUT): Requires all fields and completely replaces the resource
- Validation: PATCH validates only provided fields, PUT validates all required fields
- Implementation: PATCH modifies only specified fields, PUT replaces the entire record

Database Implementation:
- Define a robust Sequelize model with appropriate types and validations for all fields
- Implement an overwrite method that completely replaces a record using transactions
- Handle database errors properly and convert to appropriate HTTP errors

API Flow:
- Routes: Define all CRUD routes with special focus on PUT /:id for complete overwrite
- Controller: Parse ID, validate complete request body, call service, return overwritten resource
- Service: Call DAO overwrite, check affected rows, return completely replaced entity
- Validation: Define OverwriteDto with all required fields and strict validation rules
- Error Handling: Implement custom error classes and global error handler

Be thorough in generating the complete application structure with all CRUD operations, but ensure the OVERWRITE functionality is implemented correctly according to REST principles. The code should be production-ready, secure, and follow best practices for TypeScript and Express.
"""
