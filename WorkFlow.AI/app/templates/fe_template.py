import os
from pathlib import Path
import tarfile
import tempfile
from typing import Any
import jinja2
import requests
from app.models.pydantic_models.fe_gen import BoilerplateRequest, CommonComponentRequest, ScreenRequest
from app.utils.logger import get_logger

logger = get_logger(__name__)

class FrontendTemplateManager:
    """Manages frontend framework templates for code generation."""

    def __init__(self):
        self.base_template = (
            "You are an expert frontend developer.\n"
            "Follow best practices.\n"
            "Use the latest version of the framework.\n"
        )
        self.boilerplate_dir = Path(__file__).parent.parent  / "boilerplate"
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(self.boilerplate_dir),
            autoescape=False
        )

    def get_boilerplate_template(self, request: BoilerplateRequest) -> str:
        """Return a boilerplate project generation template based on the request."""
        project_structure = self._get_project_structure(request)
        print(f"Project structure: {project_structure}")
        return self.base_template + f"""

Objective: Generate a complete production-quality frontend codebase based on the following requirements.​\n


For each file, use the following format:

# File: path/to/filename.ext
```{request.language.lower()}
// Code here

TECH STACK:
Language: {request.language.title()}
Framework: {request.framework}
Styling: {request.styling}
Bundler: {request.Bundler}
State Management: {request.state_management}
Routing: {request.routing}
Testing: {request.testing}
Linting: {request.linting}
Code Style: Prettier
Version Control: Git


PROJECT STRUCTURE:
{project_structure}


Instructions:

1. Adhere strictly to the project structure guidelines provided above.
2. Use functional components and React hooks.
3. Ensure accessibility and responsiveness best practices are followed.
4. Implement {request.state_management} for state management and React Router for navigation.
5. Include necessary configuration files: package.json, .eslintrc, .prettierrc, .gitignore, and README.md.
6. Provide a README.md file with instructions on how to run and test the application.
7. If APIs are mentioned, add Axios service files with mock integration.
8. Use absolute imports throughout the codebase.
"""

    def _get_project_structure(self, request: BoilerplateRequest) -> str:
        """Return project folder structure based on framework."""
        framework = request.framework.lower()

        #Try to load from .md first
        md_structure = self._load_md_template(framework, request)
        if md_structure:
            print(f"Loaded from .md: {md_structure}")
            return md_structure
        
        else:
            return "Project structure template is not available for this framework."
        #TO DO ------- CREATE A REPO FOR EACH FRAMEWORK AND GET THE LATEST BOILERPLATE

        # COMMENTED OUT FOR NOW ----- CODE TO FETCH LATEST VERSION FROM NPM REGISTRY
        # if framework == "react":
        #     try:
        #         # Step 1: Get the latest version from npm registry
        #         response = requests.get("https://registry.npmjs.org/cra-template")
        #         latest_version = response.json()["dist-tags"]["latest"]
                
        #         # Step 2: Download the .tgz for that version
        #         TEMPLATE_URL = f"https://registry.npmjs.org/cra-template/-/cra-template-{latest_version}.tgz"
        #         temp_dir = tempfile.mkdtemp()
        #         tgz_path = os.path.join(temp_dir, "template.tgz")

        #         response = requests.get(TEMPLATE_URL)
        #         with open(tgz_path, "wb") as f:
        #             f.write(response.content)

        #         with tarfile.open(tgz_path, "r:gz") as tar:
        #             tar.extractall(temp_dir)

        #         extracted_folder = os.path.join(temp_dir, "package", "template")

        #         project_structure = f"{request.project_name}/\n"
        #         for root, dirs, files in os.walk(extracted_folder):
        #             level = root.replace(extracted_folder, '').count(os.sep)
        #             if level == 0:
        #                 for f in files:
        #                     project_structure += f"    ├── {f}\n"
        #                 for d in dirs:
        #                     project_structure += f"    ├── {d}/\n"
        #             else:
        #                 indent = "    " * level
        #                 project_structure += f"{indent}├── {os.path.basename(root)}/\n"
        #                 for f in files:
        #                     project_structure += f"{indent}    ├── {f}\n"


        #         return project_structure

        #     except Exception as e:
        #         return f"Error fetching project structure: {e}"

        # elif framework == "nextjs":
        #     try:
        #         # Step 1: Get the latest version of create-next-app
        #         response = requests.get("https://registry.npmjs.org/create-next-app")
        #         latest_version = response.json()["dist-tags"]["latest"]

        #         # Step 2: Download the .tgz for that version
        #         TEMPLATE_URL = f"https://registry.npmjs.org/create-next-app/-/create-next-app-{latest_version}.tgz"
        #         temp_dir = tempfile.mkdtemp()
        #         tgz_path = os.path.join(temp_dir, "template.tgz")

        #         response = requests.get(TEMPLATE_URL)
        #         with open(tgz_path, "wb") as f:
        #             f.write(response.content)

        #         # Extract
        #         with tarfile.open(tgz_path, "r:gz") as tar:
        #             tar.extractall(temp_dir)

        #         extracted_folder = os.path.join(temp_dir, "package")

        #         project_structure = f"{request.project_name}/\n"
        #         for root, dirs, files in os.walk(extracted_folder):
        #             level = root.replace(extracted_folder, '').count(os.sep)
        #             indent = "    " * level
        #             project_structure += f"{indent}├── {os.path.basename(root)}/\n"
        #             for f in files:
        #                 project_structure += f"{indent}    ├── {f}\n"

        #         return project_structure

        #     except Exception as e:
        #         return f"Error fetching project structure: {e}"

        # else:
        #     return "Project structure template is not available for this framework."
    def _load_md_template(self, framework: str, request: BoilerplateRequest) -> str:
        try:
            template_path = f"{framework}.md"
            print(f"Looking for template: {template_path}")  # Debug
            template = self.env.get_template(template_path)
            print("Template found! Rendering...")  # Debug
            rendered = template.render(
                project_name=request.project_name,
                language=request.language,
                js_or_ts="ts" if request.language == "typescript" else "js",
                state_management=request.state_management,
                styling=request.styling,
                modular_architecture=True
            )
            print("Rendered successfully!")  # Debug
            return rendered
        except jinja2.TemplateNotFound as e:
            print(f"Template not found: {e}")  # Debug
            return None
        except Exception as e:
            logger.error(f"Template error: {e}")
            return None

    def get_screen_template(self, request: ScreenRequest) -> str:
        """Return a single screen/page/component generation template."""
        return self.base_template + f"""
    Objective: Generate a reusable and modular screen/component based on the following requirements.\n

    For each file, use the following format:

    # File: path/to/filename.ext
    ```{request.language.lower()}
    // code here
    ```

Folder Structure:
src/
├── screens/
│   └── {request.screen_name}/
│       ├── {request.screen_name}.tsx               # Main functional component
│       ├── {request.screen_name}.module.scss       # Scoped styling ({request.styling})
│       ├── {request.screen_name}.test.tsx          # Unit tests ({request.testing})
│       ├── {request.screen_name}.types.ts          # TypeScript interfaces & props
│       ├── {request.screen_name}.constants.ts      # Static text, enums, config
│       ├── {request.screen_name}.helpers.ts        # Utility/helper functions
│       └── components/                             # Local subcomponents
│           ├── SubComponent.tsx
│           └── SubComponent.module.scss
├── assets/                                         # Global images, icons, fonts
├── hooks/                                          # Reusable custom React hooks
│   └── useFetch.ts
├── context/                                        # Global context providers
│   └── ThemeContext.tsx
├── utils/                                          # Global utility functions
│   └── formatDate.ts
├── constants/                                      # Global constants (API URLs, enums)
│   └── app.constants.ts
├── routes/                                         # Screen route definitions ({request.routing})
│   └── index.tsx
├── App.tsx                                         # Root app component
├── index.tsx                                       # ReactDOM entry point
├── vite.config.ts | webpack.config.js              # Build configuration
├── tsconfig.json                                   # TypeScript configuration
├── .env                                            # Environment variables
├── .gitignore                                      # Git ignore file
└── package.json                                    # Project dependencies and scripts

    File Details:

    1. {request.screen_name}.tsx:
        -Functional component implementation.
        -Include props typing and state management using {request.state_management}.
        -Use {request.routing} for navigation if applicable.

    2. {request.screen_name}.test.tsx:
        -Unit tests for the component using {request.testing}.
        -Cover all edge cases and ensure 100% test coverage.

    3. {request.screen_name}.module.scss:
        -Styling for the component following {request.styling} conventions.

    Instructions:
    -Implement as a functional component using {request.framework} best practices (e.g., React Hooks).
    -Code in {request.language.title()} with strict typing and clear variable names.
    -Follow {request.styling} best practices as detailed in the File Details section.
    -Accessibility: Adhere strictly to WCAG 2.1 Level AA guidelines. Ensure semantic HTML, appropriate ARIA roles/attributes where necessary, and full keyboard navigability. Test with screen readers if possible.
    -Performance: Optimize for performance. Consider techniques like code-splitting (e.g., React.lazy), memoization (e.g., React.memo, useMemo, useCallback), virtualized lists for long data sets, and efficient data fetching/state management strategies. Avoid unnecessary re-renders.
    -Implement robust data fetching, loading, and error handling patterns as shown in the examples.
    -Data Mocking / Placeholders: Provide realistic placeholder content (e.g., skeleton loaders) during loading states. If the screen interacts with APIs, include mock data structures or integrate with a mocking utility (like MSW) for isolated development and testing.
    -Preferred Libraries: If any specific libraries (e.g., for date handling like date-fns, charting like recharts, animations like framer-motion) are standard in the project, utilize them where appropriate. [Note: This could be made more specific if a 'preferred_libraries' field is added to the request model].
    -Write comprehensive unit tests using {request.testing}, mocking external dependencies (APIs, routing, state). Aim for high coverage of component logic and states.
    -Ensure the generated code adheres strictly to the specified file structure, naming conventions, and implementation details.
    """
    def get_common_component_template(self, request: CommonComponentRequest) -> str:
        """Return a template for generating common/reusable components."""
        return self.base_template + f"""
    You are generating a reusable common component for a {request.framework.title()} application.

    For each file, use the following format:
    # File: path/to/filename.{request.language.lower() == 'typescript' and 'tsx' or 'jsx'}
    ```{request.language.lower()}
    // code here
    ```

    # File: path/to/filename.test.{request.language.lower() == 'typescript' and 'tsx' or 'jsx'}
    ```{request.language.lower()}
    // test code here
    ```

    Instructions:

    IMPORTANT: Follow this exact TypeScript project structure:

    {{project_name}}/
        └──src/
            └── components/                     // Central index.ts exports all components
                    ├── ComponentName/
                    |       ├── ComponentName.tsx
                    |       └── ComponentName.css
                    └── index.ts                    // Exports all components from their folders

    1. Create a highly reusable component following these guidelines:
    - Use functional components with proper {request.language} typing
    - Follow {request.styling} best practices
    - Include proper prop validation
    - Add JSDoc documentation
    - Include usage examples in comments

    2. Component Requirements:
    Framework: {request.framework}
    Language: {request.language}
    Styling: {request.styling}

    3. The component should be:
    - Well-documented
    - Properly typed
    - Easy to import and use
    - Following best practices for {request.framework}

    4. User's Requirements:
    {request.prompt}


        """
