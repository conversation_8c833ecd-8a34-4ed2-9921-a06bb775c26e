# WorkFlow.AI

AI-powered workflow automation and code generation platform.

## Features

- **Code Generation API**: Generate code using multiple LLM providers (OpenAI, Groq, Ollama)
- **File Operations**: Read, write, and manage code files
- **Code Validation**: Validate generated code for various languages
- **Multiple LLM Provider Support**: Mix and match different LLM providers as needed

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/workflow.ai.git
   cd workflow.ai
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements/dev.txt
   ```

4. Create a `.env` file based on the provided example:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file to add your API keys and configuration.

## Usage

### Running the API server

```bash
uvicorn app.main:app --reload
```

The API will be available at `http://localhost:8000`.

API documentation is available at:
- Swagger UI: `http://localhost:8000/api/v1/docs`
- ReDoc: `http://localhost:8000/api/v1/redoc`

### Using the Code Generation API

#### Generate code

```bash
curl -X POST "http://localhost:8000/api/v1/codegen/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a function that calculates the factorial of a number",
    "language": "python",
    "provider": "openai",
    "model": "gpt-4o",
    "temperature": 0.7
  }'
```

#### Generate code with context

```bash
curl -X POST "http://localhost:8000/api/v1/codegen/generate-with-context" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Add a new endpoint to handle user profiles",
    "language": "python",
    "provider": "openai",
    "context_files": [
      {
        "filename": "app/main.py",
        "content": "# Existing main.py content"
      }
    ]
  }'
```

## Project Structure

```
workFlow.AI/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI instance initialization
│   ├── core/                # Core configuration
│   ├── api/                 # API endpoints
│   ├── models/              # Pydantic and DB models
│   ├── services/            # Business logic
│   ├── ai/                  # GenAI components
│   └── utils/               # Utility functions
├── tests/                   # Test suite
├── alembic/                 # Database migrations
├── requirements/            # Dependency files
└── scripts/                 # Maintenance scripts
```

# GitLab Integration

This module provides GitLab integration for WorkFlow.AI, allowing the application to create GitLab repositories and push generated code to them.

## Environment Variables

To use this module, set the following environment variables:

```
GITLAB_URL=https://gitlab.com  # Your GitLab instance URL
GITLAB_TOKEN=your_personal_access_token  # GitLab personal access token with API access
GITLAB_API_VERSION=v4  # GitLab API version
```

## API Endpoints

### Create a GitLab Project

```
POST /api/v1/git/project
```

Request body:
```json
{
  "name": "Project Name",  
  "description": "Project description",  
  "visibility": "private",  
  "initialize_with_readme": true
}
```

### Push Code to a GitLab Repository

```
POST /api/v1/git/push
```

Request body:
```json
{
  "project_id": "project_id_or_path",
  "local_path": "/path/to/generated/code",
  "branch": "main",
  "commit_message": "Initial code commit"
}
```

## GitLab Token Security

The GitLab token should be kept secure:

1. Store it in environment variables, not in code
2. Use the token with the minimum required permissions
3. Consider using CI/CD variables for production deployments
4. Rotate tokens periodically

## Error Handling

The module includes comprehensive error handling and logging for debugging issues with GitLab integration.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

Made With ❤️ by VectoScalar Technologies Pvt. Ltd.
